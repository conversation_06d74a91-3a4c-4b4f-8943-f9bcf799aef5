<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../auth/login.php');
}

$pageTitle = 'Manage Events';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add':
            $eventData = [
                'title' => trim($_POST['title']),
                'description' => trim($_POST['description']),
                'event_date' => $_POST['event_date'],
                'event_time' => $_POST['event_time'],
                'venue' => trim($_POST['venue']),
                'location' => trim($_POST['location']),
                'organizer' => trim($_POST['organizer']),
                'organizer_contact' => trim($_POST['organizer_contact']),
                'image_url' => trim($_POST['image_url']),
                'price' => (float)$_POST['price'],
                'total_tickets' => (int)$_POST['total_tickets'],
                'category' => trim($_POST['category'])
            ];
            
            if ($eventManager->addEvent($eventData)) {
                setFlashMessage('success', 'Event added successfully!');
            } else {
                setFlashMessage('error', 'Failed to add event.');
            }
            break;
            
        case 'delete':
            $eventId = (int)$_POST['event_id'];
            if ($eventManager->deleteEvent($eventId)) {
                setFlashMessage('success', 'Event cancelled successfully!');
            } else {
                setFlashMessage('error', 'Failed to cancel event.');
            }
            break;
    }
    
    redirect('events.php');
}

// Get all events
$events = $eventManager->getAllEvents();

$flashMessage = getFlashMessage();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                EventHub Admin
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="events.php">Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="bookings.php">Bookings</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Reports</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../">
                            <i class="fas fa-globe me-1"></i>View Site
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../user/dashboard.php">User Dashboard</a></li>
                            <li><a class="dropdown-item" href="../user/profile.php">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>
                                <i class="fas fa-calendar-alt me-2"></i>
                                Manage Events
                            </h2>
                            <p class="text-muted">Create, edit, and manage your events</p>
                        </div>
                        <button class="btn btn-primary-modern" data-bs-toggle="modal" data-bs-target="#addEventModal">
                            <i class="fas fa-plus me-2"></i>Add New Event
                        </button>
                    </div>
                </div>
            </div>

            <?php if ($flashMessage): ?>
                <div class="alert alert-<?php echo $flashMessage['type'] === 'error' ? 'danger' : $flashMessage['type']; ?> alert-dismissible fade show">
                    <?php echo $flashMessage['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Events Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card-modern">
                        <div class="card-header">
                            <h5 class="mb-0">All Events</h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($events)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Event</th>
                                                <th>Date & Time</th>
                                                <th>Location</th>
                                                <th>Price</th>
                                                <th>Tickets</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($events as $event): ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <img src="<?php echo $event->image_url ?: 'https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80'; ?>"
                                                                 alt="<?php echo htmlspecialchars($event->title); ?>"
                                                                 class="rounded me-3" style="width: 60px; height: 60px; object-fit: cover;">
                                                            <div>
                                                                <h6 class="mb-0"><?php echo htmlspecialchars($event->title); ?></h6>
                                                                <small class="text-muted"><?php echo htmlspecialchars($event->category); ?></small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <?php echo formatDate($event->event_date); ?><br>
                                                        <small class="text-muted"><?php echo formatTime($event->event_time); ?></small>
                                                    </td>
                                                    <td>
                                                        <?php echo htmlspecialchars($event->venue); ?><br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($event->location); ?></small>
                                                    </td>
                                                    <td><?php echo formatCurrency($event->price); ?></td>
                                                    <td>
                                                        <span class="badge bg-info"><?php echo $event->available_tickets; ?></span>
                                                        / <?php echo $event->total_tickets; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $event->status === 'active' ? 'success' : 'danger'; ?>">
                                                            <?php echo ucfirst($event->status); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="../events/details.php?id=<?php echo $event->id; ?>" 
                                                               class="btn btn-outline-primary" target="_blank">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <button class="btn btn-outline-warning" 
                                                                    onclick="editEvent(<?php echo $event->id; ?>)">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <?php if ($event->status === 'active'): ?>
                                                                <form method="POST" style="display: inline;">
                                                                    <input type="hidden" name="action" value="delete">
                                                                    <input type="hidden" name="event_id" value="<?php echo $event->id; ?>">
                                                                    <button type="submit" class="btn btn-outline-danger" 
                                                                            onclick="return confirm('Are you sure you want to cancel this event?')">
                                                                        <i class="fas fa-ban"></i>
                                                                    </button>
                                                                </form>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                    <h4>No Events Found</h4>
                                    <p class="text-muted">Start by creating your first event.</p>
                                    <button class="btn btn-primary-modern" data-bs-toggle="modal" data-bs-target="#addEventModal">
                                        <i class="fas fa-plus me-2"></i>Add New Event
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add Event Modal -->
    <div class="modal fade" id="addEventModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Event</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">
                        
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="title" class="form-label">Event Title *</label>
                                <input type="text" class="form-control form-control-modern" id="title" name="title" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="category" class="form-label">Category *</label>
                                <select class="form-control form-control-modern" id="category" name="category" required>
                                    <option value="">Select Category</option>
                                    <option value="Technology">Technology</option>
                                    <option value="Music">Music</option>
                                    <option value="Business">Business</option>
                                    <option value="Art">Art</option>
                                    <option value="Sports">Sports</option>
                                    <option value="Education">Education</option>
                                    <option value="Food">Food</option>
                                    <option value="Health">Health</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description *</label>
                            <textarea class="form-control form-control-modern" id="description" name="description" rows="4" required></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="event_date" class="form-label">Event Date *</label>
                                <input type="date" class="form-control form-control-modern" id="event_date" name="event_date" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="event_time" class="form-label">Event Time *</label>
                                <input type="time" class="form-control form-control-modern" id="event_time" name="event_time" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="venue" class="form-label">Venue *</label>
                                <input type="text" class="form-control form-control-modern" id="venue" name="venue" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="location" class="form-label">City/Location *</label>
                                <input type="text" class="form-control form-control-modern" id="location" name="location" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="organizer" class="form-label">Organizer *</label>
                                <input type="text" class="form-control form-control-modern" id="organizer" name="organizer" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="organizer_contact" class="form-label">Organizer Contact *</label>
                                <input type="text" class="form-control form-control-modern" id="organizer_contact" name="organizer_contact" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="price" class="form-label">Price (<?php echo CURRENCY_SYMBOL; ?>) *</label>
                                <input type="number" class="form-control form-control-modern" id="price" name="price" min="0" step="100" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="total_tickets" class="form-label">Total Tickets *</label>
                                <input type="number" class="form-control form-control-modern" id="total_tickets" name="total_tickets" min="1" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="image_url" class="form-label">Image URL</label>
                                <input type="url" class="form-control form-control-modern" id="image_url" name="image_url" 
                                       placeholder="https://example.com/image.jpg">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary-modern">
                            <i class="fas fa-save me-2"></i>Create Event
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>
    
    <script>
        // Set minimum date to today
        document.getElementById('event_date').min = new Date().toISOString().split('T')[0];
        
        function editEvent(eventId) {
            // This would open an edit modal with event data
            alert('Edit functionality would be implemented here for event ID: ' + eventId);
        }
    </script>
</body>
</html>
