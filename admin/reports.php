<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../auth/login.php');
}

$pageTitle = 'Reports & Analytics';

// Get date range from query parameters
$startDate = $_GET['start_date'] ?? date('Y-m-01'); // First day of current month
$endDate = $_GET['end_date'] ?? date('Y-m-d'); // Today

// Revenue by date
$db->query('SELECT 
    DATE(created_at) as booking_date,
    COUNT(*) as bookings_count,
    SUM(total_amount) as daily_revenue
    FROM bookings 
    WHERE booking_status = "confirmed" 
    AND DATE(created_at) BETWEEN :start_date AND :end_date
    GROUP BY DATE(created_at)
    ORDER BY booking_date DESC');
$db->bind(':start_date', $startDate);
$db->bind(':end_date', $endDate);
$dailyRevenue = $db->resultset();

// Top performing events
$db->query('SELECT 
    e.title,
    e.category,
    COUNT(b.id) as total_bookings,
    SUM(b.quantity) as tickets_sold,
    SUM(b.total_amount) as revenue
    FROM events e
    LEFT JOIN bookings b ON e.id = b.event_id AND b.booking_status = "confirmed"
    WHERE DATE(b.created_at) BETWEEN :start_date AND :end_date
    GROUP BY e.id
    HAVING total_bookings > 0
    ORDER BY revenue DESC
    LIMIT 10');
$db->bind(':start_date', $startDate);
$db->bind(':end_date', $endDate);
$topEvents = $db->resultset();

// Revenue by category
$db->query('SELECT 
    e.category,
    COUNT(b.id) as bookings_count,
    SUM(b.total_amount) as category_revenue
    FROM events e
    LEFT JOIN bookings b ON e.id = b.event_id AND b.booking_status = "confirmed"
    WHERE DATE(b.created_at) BETWEEN :start_date AND :end_date
    GROUP BY e.category
    HAVING bookings_count > 0
    ORDER BY category_revenue DESC');
$db->bind(':start_date', $startDate);
$db->bind(':end_date', $endDate);
$categoryRevenue = $db->resultset();

// Summary statistics
$db->query('SELECT 
    COUNT(*) as total_bookings,
    SUM(total_amount) as total_revenue,
    AVG(total_amount) as avg_booking_value,
    SUM(quantity) as total_tickets_sold
    FROM bookings 
    WHERE booking_status = "confirmed" 
    AND DATE(created_at) BETWEEN :start_date AND :end_date');
$db->bind(':start_date', $startDate);
$db->bind(':end_date', $endDate);
$summary = $db->single();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                EventHub Admin
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="bookings.php">Bookings</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="reports.php">Reports</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../">
                            <i class="fas fa-globe me-1"></i>View Site
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../user/dashboard.php">User Dashboard</a></li>
                            <li><a class="dropdown-item" href="../user/profile.php">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>
                                <i class="fas fa-chart-bar me-2"></i>
                                Reports & Analytics
                            </h2>
                            <p class="text-muted">Analyze your event performance and revenue</p>
                        </div>
                        
                        <!-- Date Range Filter -->
                        <form method="GET" class="d-flex align-items-center">
                            <label class="form-label me-2 mb-0">Date Range:</label>
                            <input type="date" name="start_date" class="form-control form-control-sm me-2" 
                                   value="<?php echo $startDate; ?>">
                            <span class="me-2">to</span>
                            <input type="date" name="end_date" class="form-control form-control-sm me-2" 
                                   value="<?php echo $endDate; ?>">
                            <button type="submit" class="btn btn-primary-modern btn-sm">
                                <i class="fas fa-filter me-1"></i>Filter
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Summary Statistics -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card-modern">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-primary text-white rounded-circle me-3">
                                    <i class="fas fa-ticket-alt"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo number_format($summary->total_bookings ?: 0); ?></h3>
                                    <p class="text-muted mb-0">Total Bookings</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card-modern">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-success text-white rounded-circle me-3">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo formatCurrency($summary->total_revenue ?: 0); ?></h3>
                                    <p class="text-muted mb-0">Total Revenue</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card-modern">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-info text-white rounded-circle me-3">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo formatCurrency($summary->avg_booking_value ?: 0); ?></h3>
                                    <p class="text-muted mb-0">Avg. Booking Value</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card-modern">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-warning text-white rounded-circle me-3">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo number_format($summary->total_tickets_sold ?: 0); ?></h3>
                                    <p class="text-muted mb-0">Tickets Sold</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Daily Revenue Chart -->
                <div class="col-lg-8 mb-4">
                    <div class="card-modern">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                Daily Revenue
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="dailyRevenueChart" height="100"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Category Revenue -->
                <div class="col-lg-4 mb-4">
                    <div class="card-modern">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-pie me-2"></i>
                                Revenue by Category
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="categoryChart" height="150"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Performing Events -->
            <div class="row">
                <div class="col-12">
                    <div class="card-modern">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-trophy me-2"></i>
                                Top Performing Events
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($topEvents)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Event</th>
                                                <th>Category</th>
                                                <th>Bookings</th>
                                                <th>Tickets Sold</th>
                                                <th>Revenue</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($topEvents as $index => $event): ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <span class="badge bg-primary me-2">#<?php echo $index + 1; ?></span>
                                                            <?php echo htmlspecialchars($event->title); ?>
                                                        </div>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($event->category); ?></td>
                                                    <td><?php echo number_format($event->total_bookings); ?></td>
                                                    <td><?php echo number_format($event->tickets_sold); ?></td>
                                                    <td class="fw-bold text-success"><?php echo formatCurrency($event->revenue); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                    <h4>No Data Available</h4>
                                    <p class="text-muted">No bookings found for the selected date range.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>
    
    <!-- Charts -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Daily Revenue Chart
            const dailyCtx = document.getElementById('dailyRevenueChart').getContext('2d');
            const dailyData = <?php echo json_encode(array_reverse($dailyRevenue)); ?>;
            
            const dailyLabels = dailyData.map(item => {
                const date = new Date(item.booking_date);
                return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            });
            
            const dailyRevenues = dailyData.map(item => parseFloat(item.daily_revenue) || 0);
            
            new Chart(dailyCtx, {
                type: 'line',
                data: {
                    labels: dailyLabels,
                    datasets: [{
                        label: 'Daily Revenue',
                        data: dailyRevenues,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.1,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('fr-FR').format(value) + ' <?php echo CURRENCY_SYMBOL; ?>';
                                }
                            }
                        }
                    }
                }
            });

            // Category Revenue Chart
            const categoryCtx = document.getElementById('categoryChart').getContext('2d');
            const categoryData = <?php echo json_encode($categoryRevenue); ?>;
            
            if (categoryData.length > 0) {
                const categoryLabels = categoryData.map(item => item.category);
                const categoryRevenues = categoryData.map(item => parseFloat(item.category_revenue));
                
                new Chart(categoryCtx, {
                    type: 'doughnut',
                    data: {
                        labels: categoryLabels,
                        datasets: [{
                            data: categoryRevenues,
                            backgroundColor: [
                                '#FF6384',
                                '#36A2EB',
                                '#FFCE56',
                                '#4BC0C0',
                                '#9966FF',
                                '#FF9F40'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        });
    </script>
</body>
</html>
