<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../auth/login.php');
}

$pageTitle = 'Manage Bookings';

// Handle booking status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $bookingId = (int)($_POST['booking_id'] ?? 0);
    
    switch ($action) {
        case 'confirm':
            if ($bookingManager->confirmBooking($bookingId)) {
                setFlashMessage('success', 'Booking confirmed successfully!');
            } else {
                setFlashMessage('error', 'Failed to confirm booking.');
            }
            break;
            
        case 'cancel':
            if ($bookingManager->cancelBooking($bookingId)) {
                setFlashMessage('success', 'Booking cancelled successfully!');
            } else {
                setFlashMessage('error', 'Failed to cancel booking.');
            }
            break;
    }
    
    redirect('bookings.php');
}

// Get all bookings with event and user details
$db->query('SELECT b.*, e.title, e.event_date, e.event_time, e.venue, e.location,
           u.first_name, u.last_name, u.email, u.phone
           FROM bookings b 
           JOIN events e ON b.event_id = e.id 
           JOIN users u ON b.user_id = u.id 
           ORDER BY b.created_at DESC');
$bookings = $db->resultset();

// Get booking statistics
$db->query('SELECT 
    COUNT(*) as total_bookings,
    SUM(CASE WHEN booking_status = "confirmed" THEN 1 ELSE 0 END) as confirmed_bookings,
    SUM(CASE WHEN booking_status = "pending" THEN 1 ELSE 0 END) as pending_bookings,
    SUM(CASE WHEN booking_status = "cancelled" THEN 1 ELSE 0 END) as cancelled_bookings,
    SUM(CASE WHEN booking_status = "confirmed" THEN total_amount ELSE 0 END) as total_revenue
    FROM bookings');
$stats = $db->single();

$flashMessage = getFlashMessage();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                EventHub Admin
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="bookings.php">Bookings</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Reports</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../">
                            <i class="fas fa-globe me-1"></i>View Site
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../user/dashboard.php">User Dashboard</a></li>
                            <li><a class="dropdown-item" href="../user/profile.php">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h2>
                        <i class="fas fa-ticket-alt me-2"></i>
                        Manage Bookings
                    </h2>
                    <p class="text-muted">View and manage all event bookings</p>
                </div>
            </div>

            <?php if ($flashMessage): ?>
                <div class="alert alert-<?php echo $flashMessage['type'] === 'error' ? 'danger' : $flashMessage['type']; ?> alert-dismissible fade show">
                    <?php echo $flashMessage['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card-modern">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-primary text-white rounded-circle me-3">
                                    <i class="fas fa-ticket-alt"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo number_format($stats->total_bookings); ?></h3>
                                    <p class="text-muted mb-0">Total Bookings</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card-modern">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-success text-white rounded-circle me-3">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo number_format($stats->confirmed_bookings); ?></h3>
                                    <p class="text-muted mb-0">Confirmed</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card-modern">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-warning text-white rounded-circle me-3">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo number_format($stats->pending_bookings); ?></h3>
                                    <p class="text-muted mb-0">Pending</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card-modern">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-info text-white rounded-circle me-3">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo formatCurrency($stats->total_revenue); ?></h3>
                                    <p class="text-muted mb-0">Revenue</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bookings Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card-modern">
                        <div class="card-header">
                            <h5 class="mb-0">All Bookings</h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($bookings)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Booking Reference</th>
                                                <th>Customer</th>
                                                <th>Event</th>
                                                <th>Date & Time</th>
                                                <th>Quantity</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                                <th>Booking Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($bookings as $booking): ?>
                                                <tr>
                                                    <td>
                                                        <span class="badge bg-primary"><?php echo $booking->booking_reference; ?></span>
                                                    </td>
                                                    <td>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($booking->first_name . ' ' . $booking->last_name); ?></strong><br>
                                                            <small class="text-muted"><?php echo htmlspecialchars($booking->email); ?></small><br>
                                                            <small class="text-muted"><?php echo htmlspecialchars($booking->phone); ?></small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($booking->title); ?></strong><br>
                                                            <small class="text-muted"><?php echo htmlspecialchars($booking->venue . ', ' . $booking->location); ?></small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <?php echo formatDate($booking->event_date); ?><br>
                                                        <small class="text-muted"><?php echo formatTime($booking->event_time); ?></small>
                                                    </td>
                                                    <td><?php echo $booking->quantity; ?></td>
                                                    <td><?php echo formatCurrency($booking->total_amount); ?></td>
                                                    <td>
                                                        <?php
                                                        $statusClass = 'secondary';
                                                        switch ($booking->booking_status) {
                                                            case 'confirmed': $statusClass = 'success'; break;
                                                            case 'pending': $statusClass = 'warning'; break;
                                                            case 'cancelled': $statusClass = 'danger'; break;
                                                        }
                                                        ?>
                                                        <span class="badge bg-<?php echo $statusClass; ?>">
                                                            <?php echo ucfirst($booking->booking_status); ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo formatDate($booking->created_at); ?></td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-outline-info" 
                                                                    onclick="viewBookingDetails(<?php echo $booking->id; ?>)">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            
                                                            <?php if ($booking->booking_status === 'pending'): ?>
                                                                <form method="POST" style="display: inline;">
                                                                    <input type="hidden" name="action" value="confirm">
                                                                    <input type="hidden" name="booking_id" value="<?php echo $booking->id; ?>">
                                                                    <button type="submit" class="btn btn-outline-success" 
                                                                            onclick="return confirm('Confirm this booking?')">
                                                                        <i class="fas fa-check"></i>
                                                                    </button>
                                                                </form>
                                                            <?php endif; ?>
                                                            
                                                            <?php if ($booking->booking_status !== 'cancelled'): ?>
                                                                <form method="POST" style="display: inline;">
                                                                    <input type="hidden" name="action" value="cancel">
                                                                    <input type="hidden" name="booking_id" value="<?php echo $booking->id; ?>">
                                                                    <button type="submit" class="btn btn-outline-danger" 
                                                                            onclick="return confirm('Cancel this booking? This will restore ticket availability.')">
                                                                        <i class="fas fa-times"></i>
                                                                    </button>
                                                                </form>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                                    <h4>No Bookings Found</h4>
                                    <p class="text-muted">No bookings have been made yet.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>
    
    <script>
        function viewBookingDetails(bookingId) {
            // This would open a modal with detailed booking information
            alert('View booking details for booking ID: ' + bookingId);
        }
    </script>
</body>
</html>
