<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../auth/login.php');
}

$pageTitle = 'Admin Dashboard';

// Get dashboard statistics
$totalEvents = $eventManager->getTotalEventsCount();
$totalUsers = $userManager->getTotalUsersCount();
$totalBookings = $bookingManager->getTotalBookingsCount();

// Get recent bookings
$db->query('SELECT b.*, e.title, u.first_name, u.last_name 
           FROM bookings b 
           JOIN events e ON b.event_id = e.id 
           JOIN users u ON b.user_id = u.id 
           ORDER BY b.created_at DESC 
           LIMIT 5');
$recentBookings = $db->resultset();

// Get revenue statistics
$db->query('SELECT SUM(total_amount) as total_revenue FROM bookings WHERE booking_status = "confirmed"');
$revenueResult = $db->single();
$totalRevenue = $revenueResult ? $revenueResult->total_revenue : 0;

// Get monthly revenue for chart
$db->query('SELECT 
    DATE_FORMAT(created_at, "%Y-%m") as month,
    SUM(total_amount) as revenue,
    COUNT(*) as bookings
    FROM bookings 
    WHERE booking_status = "confirmed" 
    AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(created_at, "%Y-%m")
    ORDER BY month DESC
    LIMIT 12');
$monthlyStats = $db->resultset();

// Get event categories statistics
$db->query('SELECT 
    e.category,
    COUNT(*) as event_count,
    SUM(b.quantity) as tickets_sold,
    SUM(b.total_amount) as revenue
    FROM events e
    LEFT JOIN bookings b ON e.id = b.event_id AND b.booking_status = "confirmed"
    WHERE e.status = "active"
    GROUP BY e.category
    ORDER BY revenue DESC');
$categoryStats = $db->resultset();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                EventHub Admin
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="bookings.php">Bookings</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Reports</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../">
                            <i class="fas fa-globe me-1"></i>View Site
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../user/dashboard.php">User Dashboard</a></li>
                            <li><a class="dropdown-item" href="../user/profile.php">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h2>
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Admin Dashboard
                    </h2>
                    <p class="text-muted">Welcome back! Here's what's happening with your events.</p>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card-modern">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-primary text-white rounded-circle me-3">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo number_format($totalEvents); ?></h3>
                                    <p class="text-muted mb-0">Active Events</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card-modern">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-success text-white rounded-circle me-3">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo number_format($totalUsers); ?></h3>
                                    <p class="text-muted mb-0">Registered Users</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card-modern">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-info text-white rounded-circle me-3">
                                    <i class="fas fa-ticket-alt"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo number_format($totalBookings); ?></h3>
                                    <p class="text-muted mb-0">Total Bookings</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card-modern">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-warning text-white rounded-circle me-3">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo formatCurrency($totalRevenue); ?></h3>
                                    <p class="text-muted mb-0">Total Revenue</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Revenue Chart -->
                <div class="col-lg-8 mb-4">
                    <div class="card-modern">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                Monthly Revenue
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="revenueChart" height="100"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Category Statistics -->
                <div class="col-lg-4 mb-4">
                    <div class="card-modern">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-pie me-2"></i>
                                Event Categories
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($categoryStats)): ?>
                                <?php foreach ($categoryStats as $category): ?>
                                    <div class="category-stat mb-3">
                                        <div class="d-flex justify-content-between align-items-center mb-1">
                                            <span class="fw-bold"><?php echo htmlspecialchars($category->category); ?></span>
                                            <span class="text-primary"><?php echo formatCurrency($category->revenue ?: 0); ?></span>
                                        </div>
                                        <div class="d-flex justify-content-between text-muted small">
                                            <span><?php echo $category->event_count; ?> events</span>
                                            <span><?php echo $category->tickets_sold ?: 0; ?> tickets sold</span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-muted">No category data available</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Bookings -->
            <div class="row">
                <div class="col-12">
                    <div class="card-modern">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-clock me-2"></i>
                                Recent Bookings
                            </h5>
                            <a href="bookings.php" class="btn btn-outline-primary btn-sm">
                                View All Bookings
                            </a>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($recentBookings)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Booking Reference</th>
                                                <th>Customer</th>
                                                <th>Event</th>
                                                <th>Quantity</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                                <th>Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentBookings as $booking): ?>
                                                <tr>
                                                    <td>
                                                        <span class="badge bg-primary"><?php echo $booking->booking_reference; ?></span>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($booking->first_name . ' ' . $booking->last_name); ?></td>
                                                    <td><?php echo htmlspecialchars($booking->title); ?></td>
                                                    <td><?php echo $booking->quantity; ?></td>
                                                    <td><?php echo formatCurrency($booking->total_amount); ?></td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $booking->booking_status === 'confirmed' ? 'success' : 'warning'; ?>">
                                                            <?php echo ucfirst($booking->booking_status); ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo formatDate($booking->created_at); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <p class="text-muted text-center py-4">No recent bookings found</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card-modern">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <a href="events.php?action=add" class="btn btn-primary w-100">
                                        <i class="fas fa-plus me-2"></i>Add New Event
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="bookings.php" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-list me-2"></i>Manage Bookings
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="reports.php" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-chart-bar me-2"></i>View Reports
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="../" class="btn btn-outline-secondary w-100">
                                        <i class="fas fa-globe me-2"></i>View Website
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>
    
    <!-- Chart.js Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Revenue Chart
            const ctx = document.getElementById('revenueChart').getContext('2d');
            const monthlyData = <?php echo json_encode(array_reverse($monthlyStats)); ?>;
            
            const labels = monthlyData.map(item => {
                const date = new Date(item.month + '-01');
                return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
            });
            
            const revenues = monthlyData.map(item => parseFloat(item.revenue) || 0);
            
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Revenue (<?php echo CURRENCY_SYMBOL; ?>)',
                        data: revenues,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.1,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('fr-FR').format(value) + ' <?php echo CURRENCY_SYMBOL; ?>';
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return 'Revenue: ' + new Intl.NumberFormat('fr-FR').format(context.parsed.y) + ' <?php echo CURRENCY_SYMBOL; ?>';
                                }
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
