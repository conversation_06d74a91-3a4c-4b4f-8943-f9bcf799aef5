<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../auth/login.php');
}

$pageTitle = 'Admin Dashboard';

// Get dashboard statistics
$totalEvents = $eventManager->getTotalEventsCount();
$totalUsers = $userManager->getTotalUsersCount();
$totalBookings = $bookingManager->getTotalBookingsCount();

// Get recent bookings
$db->query('SELECT b.*, e.title, u.first_name, u.last_name
           FROM bookings b
           JOIN events e ON b.event_id = e.id
           JOIN users u ON b.user_id = u.id
           ORDER BY b.created_at DESC
           LIMIT 5');
$recentBookings = $db->resultset();

// Get revenue statistics
$db->query('SELECT SUM(total_amount) as total_revenue FROM bookings WHERE booking_status = "confirmed"');
$revenueResult = $db->single();
$totalRevenue = $revenueResult ? $revenueResult->total_revenue : 0;

// Get monthly revenue for chart
$db->query('SELECT
    DATE_FORMAT(created_at, "%Y-%m") as month,
    SUM(total_amount) as revenue,
    COUNT(*) as bookings
    FROM bookings
    WHERE booking_status = "confirmed"
    AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(created_at, "%Y-%m")
    ORDER BY month DESC
    LIMIT 12');
$monthlyStats = $db->resultset();

// Get event categories statistics
$db->query('SELECT
    e.category,
    COUNT(*) as event_count,
    SUM(b.quantity) as tickets_sold,
    SUM(b.total_amount) as revenue
    FROM events e
    LEFT JOIN bookings b ON e.id = b.event_id AND b.booking_status = "confirmed"
    WHERE e.status = "active"
    GROUP BY e.category
    ORDER BY revenue DESC');
$categoryStats = $db->resultset();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .admin-welcome {
            background: var(--primary-gradient);
            color: white;
            padding: var(--space-3xl) 0 var(--space-2xl);
            margin-top: 80px;
            border-radius: 0 0 40px 40px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .admin-welcome::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .admin-welcome .container-fluid {
            position: relative;
            z-index: 2;
        }

        .welcome-stats {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: var(--space-lg);
            margin-top: var(--space-lg);
        }

        .stats-card {
            background: white;
            border-radius: var(--border-radius-lg);
            padding: var(--space-xl);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .stats-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .stat-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin-bottom: var(--space-md);
            position: relative;
            overflow: hidden;
        }

        .stat-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: inherit;
            opacity: 0.1;
            border-radius: 50%;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: var(--space-xs);
        }

        .stat-label {
            color: #6b7280;
            font-weight: 600;
            font-size: 0.95rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .chart-card {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            overflow: hidden;
        }

        .chart-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: var(--space-lg);
            position: relative;
        }

        .chart-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255,255,255,0.2);
        }

        .category-stat {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: var(--border-radius-lg);
            padding: var(--space-lg);
            margin-bottom: var(--space-md);
            border-left: 4px solid var(--primary-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .category-stat::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(64,134,129,0.1) 0%, transparent 70%);
            transform: translate(30px, -30px);
        }

        .category-stat:hover {
            transform: translateX(8px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left-color: var(--secondary-color);
        }

        .recent-bookings-card {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-modern {
            margin-bottom: 0;
        }

        .table-modern thead th {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: none;
            font-weight: 600;
            color: #374151;
            padding: var(--space-lg);
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
        }

        .table-modern tbody td {
            padding: var(--space-lg);
            border-color: #f1f5f9;
            vertical-align: middle;
        }

        .table-modern tbody tr:hover {
            background: rgba(64,134,129,0.05);
        }

        .quick-actions-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: var(--border-radius-lg);
            padding: var(--space-xl);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .action-btn {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
            border-radius: var(--border-radius);
            padding: var(--space-md) var(--space-lg);
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
            text-align: center;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.25);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }

        .floating-elements::before,
        .floating-elements::after {
            content: '';
            position: absolute;
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }

        .floating-elements::before {
            top: 10%;
            right: 10%;
            animation-delay: 0s;
        }

        .floating-elements::after {
            bottom: 10%;
            left: 10%;
            animation-delay: 3s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }
    </style>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                ZARA-Events Admin
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="bookings.php">Bookings</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Reports</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../">
                            <i class="fas fa-globe me-1"></i>View Site
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../user/dashboard.php">User Dashboard</a></li>
                            <li><a class="dropdown-item" href="../user/profile.php">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Admin Welcome Section -->
    <section class="admin-welcome">
        <div class="floating-elements"></div>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="text-center">
                        <div class="mb-4">
                            <i class="fas fa-crown fa-4x mb-3" style="color: rgba(255,255,255,0.8);"></i>
                        </div>
                        <h1 class="display-4 fw-bold mb-3">
                            Admin Command Center
                        </h1>
                        <p class="lead mb-4">Welcome back, <?php echo htmlspecialchars($_SESSION['first_name']); ?>! Your ZARA-Events empire awaits.</p>

                        <div class="welcome-stats">
                            <div class="row text-center">
                                <div class="col-md-3 mb-3 mb-md-0">
                                    <div class="h3 fw-bold mb-1"><?php echo number_format($totalEvents); ?></div>
                                    <div class="small opacity-75">Active Events</div>
                                </div>
                                <div class="col-md-3 mb-3 mb-md-0">
                                    <div class="h3 fw-bold mb-1"><?php echo number_format($totalUsers); ?></div>
                                    <div class="small opacity-75">Happy Users</div>
                                </div>
                                <div class="col-md-3 mb-3 mb-md-0">
                                    <div class="h3 fw-bold mb-1"><?php echo number_format($totalBookings); ?></div>
                                    <div class="small opacity-75">Total Bookings</div>
                                </div>
                                <div class="col-md-3">
                                    <div class="h3 fw-bold mb-1"><?php echo formatCurrency($totalRevenue); ?></div>
                                    <div class="small opacity-75">Revenue Generated</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="py-5">
        <div class="container-fluid">

            <!-- Enhanced Statistics Cards -->
            <div class="row mb-5">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card text-center">
                        <div class="stat-icon bg-primary text-white mx-auto">
                            <i class="fas fa-calendar-star"></i>
                        </div>
                        <div class="stat-number"><?php echo number_format($totalEvents); ?></div>
                        <div class="stat-label">Active Events</div>
                        <div class="mt-3">
                            <a href="events.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>View All
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card text-center">
                        <div class="stat-icon bg-success text-white mx-auto">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number"><?php echo number_format($totalUsers); ?></div>
                        <div class="stat-label">Registered Users</div>
                        <div class="mt-3">
                            <span class="badge bg-success">Growing Daily</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card text-center">
                        <div class="stat-icon bg-info text-white mx-auto">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div class="stat-number"><?php echo number_format($totalBookings); ?></div>
                        <div class="stat-label">Total Bookings</div>
                        <div class="mt-3">
                            <a href="bookings.php" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-list me-1"></i>Manage
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card text-center">
                        <div class="stat-icon bg-warning text-white mx-auto">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-number"><?php echo formatCurrency($totalRevenue); ?></div>
                        <div class="stat-label">Total Revenue</div>
                        <div class="mt-3">
                            <a href="reports.php" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-chart-line me-1"></i>Reports
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-5">
                <!-- Revenue Chart -->
                <div class="col-lg-8 mb-4">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-chart-line me-2"></i>
                                Monthly Revenue Trends
                            </h5>
                            <small class="opacity-75">Track your business growth over time</small>
                        </div>
                        <div class="p-4">
                            <canvas id="revenueChart" height="100"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Category Statistics -->
                <div class="col-lg-4 mb-4">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-chart-pie me-2"></i>
                                Event Categories
                            </h5>
                            <small class="opacity-75">Performance by category</small>
                        </div>
                        <div class="p-4">
                            <?php if (!empty($categoryStats)): ?>
                                <?php foreach ($categoryStats as $index => $category): ?>
                                    <div class="category-stat">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <span class="badge bg-primary rounded-pill">#<?php echo $index + 1; ?></span>
                                                </div>
                                                <div>
                                                    <div class="fw-bold"><?php echo htmlspecialchars($category->category); ?></div>
                                                    <small class="text-muted"><?php echo $category->event_count; ?> events</small>
                                                </div>
                                            </div>
                                            <div class="text-end">
                                                <div class="fw-bold text-primary"><?php echo formatCurrency($category->revenue ?: 0); ?></div>
                                                <small class="text-muted"><?php echo $category->tickets_sold ?: 0; ?> tickets</small>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No category data available</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Bookings -->
            <div class="row mb-5">
                <div class="col-12">
                    <div class="recent-bookings-card">
                        <div class="chart-header d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-0 fw-bold">
                                    <i class="fas fa-clock me-2"></i>
                                    Recent Bookings
                                </h5>
                                <small class="opacity-75">Latest customer activity</small>
                            </div>
                            <a href="bookings.php" class="btn btn-light btn-sm">
                                <i class="fas fa-external-link-alt me-1"></i>
                                View All Bookings
                            </a>
                        </div>
                        <div class="p-0">
                            <?php if (!empty($recentBookings)): ?>
                                <div class="table-responsive">
                                    <table class="table table-modern">
                                        <thead>
                                            <tr>
                                                <th>Reference</th>
                                                <th>Customer</th>
                                                <th>Event</th>
                                                <th>Quantity</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                                <th>Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentBookings as $booking): ?>
                                                <tr>
                                                    <td>
                                                        <span class="badge bg-primary fs-6"><?php echo $booking->booking_reference; ?></span>
                                                    </td>
                                                    <td>
                                                        <div class="fw-bold"><?php echo htmlspecialchars($booking->first_name . ' ' . $booking->last_name); ?></div>
                                                    </td>
                                                    <td>
                                                        <div class="fw-bold"><?php echo htmlspecialchars($booking->title); ?></div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info"><?php echo $booking->quantity; ?> ticket<?php echo $booking->quantity > 1 ? 's' : ''; ?></span>
                                                    </td>
                                                    <td>
                                                        <div class="fw-bold text-success"><?php echo formatCurrency($booking->total_amount); ?></div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $booking->booking_status === 'confirmed' ? 'success' : 'warning'; ?> fs-6">
                                                            <i class="fas fa-<?php echo $booking->booking_status === 'confirmed' ? 'check-circle' : 'clock'; ?> me-1"></i>
                                                            <?php echo ucfirst($booking->booking_status); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="text-muted"><?php echo formatDate($booking->created_at); ?></div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                    <h5>No Recent Bookings</h5>
                                    <p class="text-muted">Bookings will appear here as customers make reservations</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="quick-actions-card">
                        <div class="text-center mb-4">
                            <h5 class="mb-2 fw-bold">
                                <i class="fas fa-rocket me-2"></i>
                                Quick Actions
                            </h5>
                            <p class="mb-0 opacity-75">Manage your events platform efficiently</p>
                        </div>
                        <div class="row">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="events.php?action=add" class="action-btn">
                                    <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                    <div class="fw-bold">Add New Event</div>
                                    <small class="opacity-75">Create exciting events</small>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="bookings.php" class="action-btn">
                                    <i class="fas fa-list-alt fa-2x mb-2"></i>
                                    <div class="fw-bold">Manage Bookings</div>
                                    <small class="opacity-75">Handle reservations</small>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="reports.php" class="action-btn">
                                    <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                    <div class="fw-bold">View Reports</div>
                                    <small class="opacity-75">Analyze performance</small>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="../" class="action-btn">
                                    <i class="fas fa-globe fa-2x mb-2"></i>
                                    <div class="fw-bold">View Website</div>
                                    <small class="opacity-75">See customer view</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <!-- Chart.js Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Revenue Chart
            const ctx = document.getElementById('revenueChart').getContext('2d');
            const monthlyData = <?php echo json_encode(array_reverse($monthlyStats)); ?>;

            const labels = monthlyData.map(item => {
                const date = new Date(item.month + '-01');
                return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
            });

            const revenues = monthlyData.map(item => parseFloat(item.revenue) || 0);

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Revenue (<?php echo CURRENCY_SYMBOL; ?>)',
                        data: revenues,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.1,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('fr-FR').format(value) + ' <?php echo CURRENCY_SYMBOL; ?>';
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return 'Revenue: ' + new Intl.NumberFormat('fr-FR').format(context.parsed.y) + ' <?php echo CURRENCY_SYMBOL; ?>';
                                }
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
