FROM php:8.1-apache

# Install essential PHP extensions
RUN docker-php-ext-install pdo pdo_mysql mysqli

# Enable Apache mod_rewrite
RUN a2enmod rewrite

# Set working directory
WORKDIR /var/www/html

# Copy application files
COPY . /var/www/html/

# Set proper permissions
RUN chown -R www-data:www-data /var/www/html

# Create uploads directory
RUN mkdir -p /var/www/html/assets/images/events

# Expose port 80
EXPOSE 80

# Start Apache
CMD ["apache2-foreground"]
