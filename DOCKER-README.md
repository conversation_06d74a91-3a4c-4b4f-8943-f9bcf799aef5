# Event Booking System - Docker Setup

This guide will help you set up the Event Booking System using Docker and MySQL.

## Prerequisites

- Docker Desktop installed on your system
- Docker Compose (usually included with Docker Desktop)
- At least 2GB of free disk space

## Quick Start

### Option 1: Automated Setup (Recommended)

1. Run the setup script:
   ```bash
   ./docker-setup.sh
   ```

2. Wait for the containers to start (this may take a few minutes on first run)

3. Access the application:
   - **Web Application**: http://localhost:8000
   - **phpMyAdmin**: http://localhost:8080
   - **Database Test**: http://localhost:8000/test-db.php

### Option 2: Manual Setup

1. Start the containers:
   ```bash
   docker-compose up -d
   ```

2. Wait for MySQL to initialize (about 30 seconds)

3. Check container status:
   ```bash
   docker-compose ps
   ```

## Default Login Credentials

### Admin Account
- **Username**: admin
- **Password**: admin123

### Test User Account
- **Username**: testuser
- **Password**: user123

### Database Access (phpMyAdmin)
- **Username**: event_user
- **Password**: event_password

## Services

| Service | Port | Description |
|---------|------|-------------|
| Web Application | 8000 | Main PHP application |
| MySQL Database | 3306 | MySQL 8.0 database |
| phpMyAdmin | 8080 | Database management interface |

## Useful Commands

### Container Management
```bash
# Start all services
docker-compose up -d

# Stop all services
docker-compose down

# Restart services
docker-compose restart

# View logs
docker-compose logs -f

# View logs for specific service
docker-compose logs -f web
docker-compose logs -f mysql
```

### Database Management
```bash
# Access MySQL CLI
docker-compose exec mysql mysql -u event_user -p

# Backup database
docker-compose exec mysql mysqldump -u event_user -p event_booking_system > backup.sql

# Restore database
docker-compose exec -T mysql mysql -u event_user -p event_booking_system < backup.sql
```

### Development
```bash
# Rebuild containers after code changes
docker-compose up -d --build

# Reset database (WARNING: This will delete all data)
docker-compose down -v
docker-compose up -d
```

## Troubleshooting

### Container Won't Start
1. Check if ports are already in use:
   ```bash
   lsof -i :8000
   lsof -i :3306
   lsof -i :8080
   ```

2. Stop conflicting services or change ports in `docker-compose.yml`

### Database Connection Issues
1. Wait for MySQL to fully initialize (can take 1-2 minutes)
2. Check MySQL logs: `docker-compose logs mysql`
3. Test connection: http://localhost:8000/test-db.php

### Permission Issues
1. Ensure Docker has proper permissions
2. On Linux/Mac, you might need to run with `sudo`

### Reset Everything
```bash
# Stop and remove all containers, networks, and volumes
docker-compose down -v
docker system prune -f

# Start fresh
./docker-setup.sh
```

## File Structure

```
├── docker-compose.yml      # Docker services configuration
├── Dockerfile             # PHP application container
├── .env                   # Environment variables
├── docker-setup.sh        # Automated setup script
├── docker/
│   └── apache-config.conf # Apache configuration
├── database/
│   ├── schema.sql         # Database schema
│   └── init-data.sql      # Sample data
└── test-db.php           # Database connection test
```

## Environment Variables

You can customize the setup by editing the `.env` file:

```env
DB_HOST=mysql
DB_USER=event_user
DB_PASS=event_password
DB_NAME=event_booking_system
MYSQL_ROOT_PASSWORD=root_password
```

## Production Considerations

For production deployment:

1. Change all default passwords
2. Use environment-specific `.env` files
3. Enable SSL/HTTPS
4. Set up proper backup strategies
5. Configure monitoring and logging
6. Use Docker secrets for sensitive data

## Support

If you encounter issues:

1. Check the logs: `docker-compose logs -f`
2. Verify all containers are running: `docker-compose ps`
3. Test database connection: http://localhost:8000/test-db.php
4. Ensure all required ports are available

## Alternative: Using Built-in PHP Server

If you prefer not to use Docker for the web server, you can:

1. Start only the database:
   ```bash
   docker-compose up -d mysql phpmyadmin
   ```

2. Update `includes/config.php` to use `localhost` instead of `mysql`

3. Run PHP built-in server:
   ```bash
   php -S localhost:8000
   ```
