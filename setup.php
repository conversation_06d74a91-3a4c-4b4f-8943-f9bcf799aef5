<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Booking System - Setup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0"><i class="fas fa-cog me-2"></i>Event Booking System Setup</h3>
                    </div>
                    <div class="card-body">
                        <?php
                        $setupComplete = false;
                        $errors = [];
                        $success = [];

                        // Check if setup is being run
                        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup'])) {
                            $dbHost = $_POST['db_host'] ?? 'localhost';
                            $dbUser = $_POST['db_user'] ?? '';
                            $dbPass = $_POST['db_pass'] ?? '';
                            $dbName = $_POST['db_name'] ?? 'event_booking_system';
                            $siteUrl = $_POST['site_url'] ?? 'http://localhost';

                            // Test database connection
                            try {
                                $dsn = "mysql:host=$dbHost;charset=utf8mb4";
                                $pdo = new PDO($dsn, $dbUser, $dbPass);
                                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                                // Create database if it doesn't exist
                                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName`");
                                $pdo->exec("USE `$dbName`");

                                // Read and execute SQL file
                                $sqlFile = 'database/schema.sql';
                                if (file_exists($sqlFile)) {
                                    $sql = file_get_contents($sqlFile);
                                    // Remove the USE statement since we already selected the database
                                    $sql = preg_replace('/USE\s+`?event_booking_system`?;?\s*/i', '', $sql);
                                    $pdo->exec($sql);
                                    $success[] = "Database created and populated successfully!";
                                } else {
                                    $errors[] = "SQL file not found: $sqlFile";
                                }

                                // Update config file
                                $configFile = 'includes/config.php';
                                if (file_exists($configFile)) {
                                    $config = file_get_contents($configFile);
                                    $config = str_replace("define('DB_HOST', 'localhost');", "define('DB_HOST', '$dbHost');", $config);
                                    $config = str_replace("define('DB_USER', 'root');", "define('DB_USER', '$dbUser');", $config);
                                    $config = str_replace("define('DB_PASS', '');", "define('DB_PASS', '$dbPass');", $config);
                                    $config = str_replace("define('DB_NAME', 'event_booking_system');", "define('DB_NAME', '$dbName');", $config);
                                    $config = str_replace("define('SITE_URL', 'http://localhost');", "define('SITE_URL', '$siteUrl');", $config);
                                    
                                    if (file_put_contents($configFile, $config)) {
                                        $success[] = "Configuration file updated successfully!";
                                        $setupComplete = true;
                                    } else {
                                        $errors[] = "Failed to update configuration file. Please check file permissions.";
                                    }
                                } else {
                                    $errors[] = "Configuration file not found: $configFile";
                                }

                            } catch (PDOException $e) {
                                $errors[] = "Database connection failed: " . $e->getMessage();
                            }
                        }

                        // Check if already configured
                        $alreadyConfigured = false;
                        if (file_exists('includes/config.php')) {
                            $config = file_get_contents('includes/config.php');
                            if (strpos($config, "define('DB_USER', 'root');") === false) {
                                $alreadyConfigured = true;
                            }
                        }
                        ?>

                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>Setup Errors:</h6>
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($success)): ?>
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle me-2"></i>Setup Success:</h6>
                                <ul class="mb-0">
                                    <?php foreach ($success as $msg): ?>
                                        <li><?php echo htmlspecialchars($msg); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <?php if ($setupComplete): ?>
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Setup Complete!</h6>
                                <p class="mb-2">Your Event Booking System has been set up successfully. You can now:</p>
                                <div class="d-grid gap-2 d-md-flex">
                                    <a href="index.php" class="btn btn-primary">
                                        <i class="fas fa-home me-2"></i>Visit Homepage
                                    </a>
                                    <a href="auth/login.php" class="btn btn-outline-primary">
                                        <i class="fas fa-sign-in-alt me-2"></i>Admin Login
                                    </a>
                                </div>
                                <hr>
                                <h6>Default Admin Account:</h6>
                                <p class="mb-0">
                                    <strong>Username:</strong> admin<br>
                                    <strong>Password:</strong> admin123
                                </p>
                            </div>
                        <?php elseif ($alreadyConfigured): ?>
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>Already Configured</h6>
                                <p class="mb-2">It appears the system has already been configured. If you need to reconfigure, please:</p>
                                <ol>
                                    <li>Backup your current database</li>
                                    <li>Reset the configuration file</li>
                                    <li>Run this setup again</li>
                                </ol>
                                <div class="d-grid gap-2 d-md-flex">
                                    <a href="index.php" class="btn btn-primary">
                                        <i class="fas fa-home me-2"></i>Visit Homepage
                                    </a>
                                    <a href="admin/index.php" class="btn btn-outline-primary">
                                        <i class="fas fa-tachometer-alt me-2"></i>Admin Panel
                                    </a>
                                </div>
                            </div>
                        <?php else: ?>
                            <h5 class="mb-4">Welcome to the Event Booking System Setup</h5>
                            <p class="text-muted mb-4">Please provide the following information to set up your system:</p>

                            <form method="POST">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="db_host" class="form-label">Database Host</label>
                                        <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="db_name" class="form-label">Database Name</label>
                                        <input type="text" class="form-control" id="db_name" name="db_name" value="event_booking_system" required>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="db_user" class="form-label">Database Username</label>
                                        <input type="text" class="form-control" id="db_user" name="db_user" value="root" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="db_pass" class="form-label">Database Password</label>
                                        <input type="password" class="form-control" id="db_pass" name="db_pass">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="site_url" class="form-label">Site URL</label>
                                    <input type="url" class="form-control" id="site_url" name="site_url" 
                                           value="<?php echo 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']); ?>" required>
                                    <div class="form-text">The base URL where your site will be accessible</div>
                                </div>

                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>Setup Information:</h6>
                                    <ul class="mb-0">
                                        <li>This will create the database and tables automatically</li>
                                        <li>Sample events and admin account will be created</li>
                                        <li>Make sure your database user has CREATE privileges</li>
                                        <li>The setup will update your configuration file</li>
                                    </ul>
                                </div>

                                <button type="submit" name="setup" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-rocket me-2"></i>Setup Event Booking System
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- System Requirements -->
                <div class="card shadow mt-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-server me-2"></i>System Requirements Check</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>PHP Requirements:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-<?php echo version_compare(PHP_VERSION, '8.0.0', '>=') ? 'check text-success' : 'times text-danger'; ?> me-2"></i>PHP 8.0+ (Current: <?php echo PHP_VERSION; ?>)</li>
                                    <li><i class="fas fa-<?php echo extension_loaded('pdo') ? 'check text-success' : 'times text-danger'; ?> me-2"></i>PDO Extension</li>
                                    <li><i class="fas fa-<?php echo extension_loaded('pdo_mysql') ? 'check text-success' : 'times text-danger'; ?> me-2"></i>PDO MySQL Extension</li>
                                    <li><i class="fas fa-<?php echo extension_loaded('session') ? 'check text-success' : 'times text-danger'; ?> me-2"></i>Session Extension</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>File Permissions:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-<?php echo is_writable('includes/config.php') ? 'check text-success' : 'times text-danger'; ?> me-2"></i>Config file writable</li>
                                    <li><i class="fas fa-<?php echo is_writable('assets/images/') ? 'check text-success' : 'times text-danger'; ?> me-2"></i>Images directory writable</li>
                                    <li><i class="fas fa-<?php echo file_exists('database/schema.sql') ? 'check text-success' : 'times text-danger'; ?> me-2"></i>Database schema exists</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
