<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../auth/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

$pageTitle = 'Shopping Cart';
$userId = $_SESSION['user_id'];

// Handle cart actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'update':
            $eventId = $_POST['event_id'] ?? 0;
            $quantity = (int)($_POST['quantity'] ?? 0);

            if ($eventId && $quantity >= 0) {
                if ($cartManager->updateCartItem($userId, $eventId, $quantity)) {
                    setFlashMessage('success', 'Cart updated successfully!');
                } else {
                    setFlashMessage('error', 'Failed to update cart item.');
                }
            }
            break;

        case 'remove':
            $eventId = $_POST['event_id'] ?? 0;

            if ($eventId) {
                if ($cartManager->removeFromCart($userId, $eventId)) {
                    setFlashMessage('success', 'Item removed from cart!');
                } else {
                    setFlashMessage('error', 'Failed to remove item from cart.');
                }
            }
            break;

        case 'clear':
            if ($cartManager->clearCart($userId)) {
                setFlashMessage('success', 'Cart cleared successfully!');
            } else {
                setFlashMessage('error', 'Failed to clear cart.');
            }
            break;
    }

    redirect('cart.php');
}

// Get cart items
$cartItems = $cartManager->getCartItems($userId);
$cartTotal = $cartManager->getCartTotal($userId);
$cartCount = $cartManager->getCartCount($userId);

$flashMessage = getFlashMessage();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">

    <style>
        body {
            background: var(--secondary-light);
        }

        .cart-item {
            transition: var(--transition-smooth);
            border-radius: var(--border-radius);
            margin-bottom: var(--space-sm);
        }

        .cart-item:hover {
            background: rgba(64, 134, 129, 0.02);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(64, 134, 129, 0.1);
        }

        .quantity-form select {
            border: 2px solid #e5e7eb;
            border-radius: var(--border-radius);
            transition: var(--transition-smooth);
        }

        .quantity-form select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(64, 134, 129, 0.1);
        }

        .cart-progress {
            background: var(--primary-gradient);
            color: white;
            border-radius: var(--border-radius);
            padding: var(--space-lg);
            margin-bottom: var(--space-xl);
        }

        .progress-step {
            display: flex;
            align-items: center;
            gap: var(--space-md);
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .step-number.active {
            background: white;
            color: var(--primary-color);
        }

        .empty-cart-animation {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                ZARA-Events
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../events/">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../events/search.php">Search</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative active" href="cart.php">
                            <i class="fas fa-shopping-cart cart-icon"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle"
                                  <?php echo $cartCount > 0 ? '' : 'style="display: none;"'; ?>>
                                <?php echo $cartCount; ?>
                            </span>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['first_name'] ?? 'User'); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../user/dashboard.php">Dashboard</a></li>
                            <li><a class="dropdown-item" href="../user/profile.php">Profile</a></li>
                            <?php if (isAdmin()): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../admin/">Admin Panel</a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Shopping Progress -->
            <?php if (!empty($cartItems)): ?>
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="cart-progress">
                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <div class="progress-step">
                                        <div class="step-number active">1</div>
                                        <div>
                                            <h6 class="mb-0">Shopping Cart</h6>
                                            <small class="opacity-75">Review your items</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="progress-step">
                                        <div class="step-number">2</div>
                                        <div>
                                            <h6 class="mb-0">Checkout</h6>
                                            <small class="opacity-75">Enter details</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="progress-step">
                                        <div class="step-number">3</div>
                                        <div>
                                            <h6 class="mb-0">Confirmation</h6>
                                            <small class="opacity-75">Complete booking</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h2>
                        <i class="fas fa-shopping-cart me-2"></i>
                        Shopping Cart
                        <?php if ($cartCount > 0): ?>
                            <span class="badge bg-primary ms-2"><?php echo $cartCount; ?> item<?php echo $cartCount > 1 ? 's' : ''; ?></span>
                        <?php endif; ?>
                    </h2>
                </div>
            </div>

            <?php if ($flashMessage): ?>
                <div class="alert alert-<?php echo $flashMessage['type'] === 'error' ? 'danger' : $flashMessage['type']; ?> alert-dismissible fade show">
                    <?php echo $flashMessage['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- Cart Items -->
                <div class="col-lg-8 mb-4">
                    <?php if (!empty($cartItems)): ?>
                        <div class="card-modern">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Cart Items</h5>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="clear">
                                    <button type="submit" class="btn btn-outline-danger btn-sm"
                                            onclick="return confirm('Are you sure you want to clear your cart?')">
                                        <i class="fas fa-trash me-1"></i>Clear Cart
                                    </button>
                                </form>
                            </div>
                            <div class="card-body p-0">
                                <?php foreach ($cartItems as $item): ?>
                                    <div class="cart-item border-bottom p-4">
                                        <div class="row align-items-center">
                                            <div class="col-md-2">
                                                <img src="<?php echo $item->image_url ?: 'https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80'; ?>"
                                                     alt="<?php echo htmlspecialchars($item->title); ?>"
                                                     class="img-fluid rounded">
                                            </div>
                                            <div class="col-md-4">
                                                <h6 class="mb-1"><?php echo htmlspecialchars($item->title); ?></h6>
                                                <div class="text-muted small">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    <?php echo formatDate($item->event_date); ?>
                                                </div>
                                                <div class="text-muted small">
                                                    <i class="fas fa-clock me-1"></i>
                                                    <?php echo formatTime($item->event_time); ?>
                                                </div>
                                                <div class="text-muted small">
                                                    <i class="fas fa-map-marker-alt me-1"></i>
                                                    <?php echo htmlspecialchars($item->venue); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="fw-bold"><?php echo formatCurrency($item->price); ?></div>
                                                <small class="text-muted">per ticket</small>
                                            </div>
                                            <div class="col-md-2">
                                                <form method="POST" class="quantity-form">
                                                    <input type="hidden" name="action" value="update">
                                                    <input type="hidden" name="event_id" value="<?php echo $item->event_id; ?>">
                                                    <select name="quantity" class="form-select form-select-sm"
                                                            onchange="this.form.submit()">
                                                        <?php for ($i = 1; $i <= min(10, $item->available_tickets); $i++): ?>
                                                            <option value="<?php echo $i; ?>"
                                                                    <?php echo $i == $item->quantity ? 'selected' : ''; ?>>
                                                                <?php echo $i; ?>
                                                            </option>
                                                        <?php endfor; ?>
                                                    </select>
                                                </form>
                                            </div>
                                            <div class="col-md-1">
                                                <div class="fw-bold text-primary">
                                                    <?php echo formatCurrency($item->price * $item->quantity); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-1">
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="remove">
                                                    <input type="hidden" name="event_id" value="<?php echo $item->event_id; ?>">
                                                    <button type="submit" class="btn btn-outline-danger btn-sm"
                                                            onclick="return confirm('Remove this item from cart?')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="card-modern text-center py-5">
                            <div class="card-body">
                                <div class="empty-cart-animation">
                                    <i class="fas fa-shopping-cart fa-4x text-muted mb-4"></i>
                                </div>
                                <h3 class="fw-bold mb-3">Your cart is empty</h3>
                                <p class="text-muted mb-4">Discover amazing events and start building your perfect experience!</p>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                    <a href="../events/" class="btn btn-primary-enhanced btn-lg">
                                        <i class="fas fa-calendar-alt me-2"></i>Browse Events
                                    </a>
                                    <a href="../events/search.php" class="btn btn-secondary-enhanced btn-lg">
                                        <i class="fas fa-search me-2"></i>Search Events
                                    </a>
                                </div>

                                <!-- Popular Categories -->
                                <div class="mt-5">
                                    <h6 class="text-muted mb-3">Popular Categories</h6>
                                    <div class="d-flex flex-wrap justify-content-center gap-2">
                                        <span class="badge bg-light text-dark px-3 py-2">
                                            <i class="fas fa-music me-1"></i>Concerts
                                        </span>
                                        <span class="badge bg-light text-dark px-3 py-2">
                                            <i class="fas fa-briefcase me-1"></i>Conferences
                                        </span>
                                        <span class="badge bg-light text-dark px-3 py-2">
                                            <i class="fas fa-graduation-cap me-1"></i>Workshops
                                        </span>
                                        <span class="badge bg-light text-dark px-3 py-2">
                                            <i class="fas fa-theater-masks me-1"></i>Entertainment
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Cart Summary -->
                <div class="col-lg-4">
                    <?php if (!empty($cartItems)): ?>
                        <div class="card-modern sticky-top" style="top: 100px;">
                            <div class="card-header">
                                <h5 class="mb-0">Order Summary</h5>
                            </div>
                            <div class="card-body">
                                <div class="summary-item d-flex justify-content-between mb-2">
                                    <span>Items (<?php echo $cartCount; ?>):</span>
                                    <span><?php echo formatCurrency($cartTotal); ?></span>
                                </div>
                                <div class="summary-item d-flex justify-content-between mb-2">
                                    <span>Service Fee:</span>
                                    <span><?php echo formatCurrency(0); ?></span>
                                </div>
                                <hr>
                                <div class="summary-total d-flex justify-content-between mb-4">
                                    <strong>Total:</strong>
                                    <strong class="text-primary"><?php echo formatCurrency($cartTotal); ?></strong>
                                </div>

                                <div class="d-grid gap-2">
                                    <a href="checkout.php" class="btn btn-primary-modern btn-lg">
                                        <i class="fas fa-credit-card me-2"></i>
                                        Proceed to Checkout
                                    </a>
                                    <a href="../events/" class="btn btn-outline-primary">
                                        <i class="fas fa-plus me-2"></i>
                                        Add More Events
                                    </a>
                                </div>

                                <!-- Security Info -->
                                <div class="mt-4 pt-3 border-top">
                                    <div class="text-center text-muted small">
                                        <i class="fas fa-shield-alt me-1"></i>
                                        Secure checkout with SSL encryption
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Continue Shopping -->
                    <div class="card-modern mt-3">
                        <div class="card-body">
                            <h6 class="fw-bold mb-3">Need Help?</h6>
                            <div class="d-grid gap-2">
                                <a href="../events/search.php" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-search me-2"></i>Search Events
                                </a>
                                <a href="#" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-headset me-2"></i>Contact Support
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-submit quantity forms with a small delay to prevent rapid submissions
            const quantityForms = document.querySelectorAll('.quantity-form');
            quantityForms.forEach(form => {
                const select = form.querySelector('select[name="quantity"]');
                if (select) {
                    select.addEventListener('change', function() {
                        setTimeout(() => {
                            form.submit();
                        }, 100);
                    });
                }
            });
        });
    </script>
</body>
</html>
