<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Please login to add items to cart',
        'redirect' => '../auth/login.php'
    ]);
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit;
}

// Get POST data
$input = json_decode(file_get_contents('php://input'), true);
$eventId = $input['event_id'] ?? 0;
$quantity = (int)($input['quantity'] ?? 1);

// Validate input
if (!$eventId || $quantity < 1) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid event ID or quantity'
    ]);
    exit;
}

// Validate quantity limit
if ($quantity > 10) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Maximum 10 tickets per event'
    ]);
    exit;
}

$userId = $_SESSION['user_id'];

try {
    // Check if event exists and is available
    $event = $eventManager->getEventById($eventId);
    
    if (!$event) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'Event not found'
        ]);
        exit;
    }
    
    if ($event->status !== 'active') {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Event is not available for booking'
        ]);
        exit;
    }
    
    if ($event->available_tickets < $quantity) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Not enough tickets available. Only ' . $event->available_tickets . ' tickets left.'
        ]);
        exit;
    }
    
    // Check if item already exists in cart
    $existingCartItems = $cartManager->getCartItems($userId);
    $existingQuantity = 0;
    
    foreach ($existingCartItems as $item) {
        if ($item->event_id == $eventId) {
            $existingQuantity = $item->quantity;
            break;
        }
    }
    
    // Check if total quantity would exceed available tickets
    $totalQuantity = $existingQuantity + $quantity;
    if ($totalQuantity > $event->available_tickets) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Cannot add ' . $quantity . ' tickets. You already have ' . $existingQuantity . ' in cart and only ' . $event->available_tickets . ' are available.'
        ]);
        exit;
    }
    
    // Add to cart
    if ($cartManager->addToCart($userId, $eventId, $quantity)) {
        // Get updated cart count
        $cartCount = $cartManager->getCartCount($userId);
        $cartTotal = $cartManager->getCartTotal($userId);
        
        echo json_encode([
            'success' => true,
            'message' => $quantity . ' ticket' . ($quantity > 1 ? 's' : '') . ' added to cart successfully!',
            'cart_count' => $cartCount,
            'cart_total' => formatCurrency($cartTotal),
            'event_title' => $event->title
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to add item to cart. Please try again.'
        ]);
    }
    
} catch (Exception $e) {
    error_log('Add to cart error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred. Please try again.'
    ]);
}
?>
