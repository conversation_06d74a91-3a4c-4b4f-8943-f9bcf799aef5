<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode([
        'success' => true,
        'cart_count' => 0,
        'cart_total' => formatCurrency(0)
    ]);
    exit;
}

$userId = $_SESSION['user_id'];

try {
    // Get cart count and total
    $cartCount = $cartManager->getCartCount($userId);
    $cartTotal = $cartManager->getCartTotal($userId);
    
    echo json_encode([
        'success' => true,
        'cart_count' => $cartCount,
        'cart_total' => formatCurrency($cartTotal)
    ]);
    
} catch (Exception $e) {
    error_log('Get cart count error: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'cart_count' => 0,
        'cart_total' => formatCurrency(0),
        'message' => 'Error loading cart information'
    ]);
}
?>
