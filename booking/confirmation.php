<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../auth/login.php');
}

$pageTitle = 'Booking Confirmation';

// Get booking references from URL
$references = $_GET['references'] ?? '';
$emailSent = $_GET['email_sent'] ?? '0';

if (empty($references)) {
    redirect('../user/dashboard.php');
}

$bookingReferences = explode(',', $references);
$bookings = [];
$totalAmount = 0;

// Get booking details
foreach ($bookingReferences as $reference) {
    $booking = $bookingManager->getBookingByReference(trim($reference));
    if ($booking) {
        $bookings[] = $booking;
        $totalAmount += $booking->total_amount;
    }
}

if (empty($bookings)) {
    redirect('../user/dashboard.php');
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                ZARA-Events
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../events/">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../events/search.php">Search</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="cart.php">
                            <i class="fas fa-shopping-cart cart-icon"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle" style="display: none;">0</span>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../user/dashboard.php">Dashboard</a></li>
                            <li><a class="dropdown-item" href="../user/profile.php">Profile</a></li>
                            <?php if (isAdmin()): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../admin/">Admin Panel</a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Checkout Progress -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="checkout-progress">
                        <div class="progress-step completed">
                            <div class="step-number">1</div>
                            <div class="step-label">Cart</div>
                        </div>
                        <div class="progress-line completed"></div>
                        <div class="progress-step completed">
                            <div class="step-number">2</div>
                            <div class="step-label">Checkout</div>
                        </div>
                        <div class="progress-line completed"></div>
                        <div class="progress-step active">
                            <div class="step-number">3</div>
                            <div class="step-label">Confirmation</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Success Message -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card-modern text-center py-5">
                        <div class="card-body">
                            <div class="success-icon mb-4">
                                <i class="fas fa-check-circle fa-5x text-success"></i>
                            </div>
                            <h2 class="text-success mb-3">Booking Confirmed!</h2>
                            <p class="lead mb-4">
                                Thank you for your booking. Your tickets have been reserved successfully.
                            </p>

                            <?php if ($emailSent === '1'): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-envelope me-2"></i>
                                    Confirmation email has been sent to your email address.
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Booking confirmed but email could not be sent. Please save your booking references below.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking Details -->
            <div class="row">
                <div class="col-lg-8 mb-4">
                    <div class="card-modern">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-ticket-alt me-2"></i>
                                Booking Details
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($bookings as $index => $booking): ?>
                                <div class="booking-item <?php echo $index > 0 ? 'border-top pt-4 mt-4' : ''; ?>">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h6 class="fw-bold"><?php echo htmlspecialchars($booking->title); ?></h6>

                                            <div class="booking-meta mb-3">
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="fas fa-calendar-alt text-primary me-2"></i>
                                                    <span><?php echo formatDate($booking->event_date); ?> at <?php echo formatTime($booking->event_time); ?></span>
                                                </div>
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                                    <span><?php echo htmlspecialchars($booking->venue . ', ' . $booking->location); ?></span>
                                                </div>
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="fas fa-user text-primary me-2"></i>
                                                    <span><?php echo htmlspecialchars($booking->attendee_name); ?></span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-envelope text-primary me-2"></i>
                                                    <span><?php echo htmlspecialchars($booking->attendee_email); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 text-md-end">
                                            <div class="booking-reference mb-2">
                                                <strong>Booking Reference:</strong><br>
                                                <span class="badge bg-primary fs-6"><?php echo $booking->booking_reference; ?></span>
                                            </div>
                                            <div class="booking-quantity mb-2">
                                                <strong>Quantity:</strong> <?php echo $booking->quantity; ?> ticket<?php echo $booking->quantity > 1 ? 's' : ''; ?>
                                            </div>
                                            <div class="booking-amount">
                                                <strong>Amount:</strong> <span class="text-primary"><?php echo formatCurrency($booking->total_amount); ?></span>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if (!empty($booking->special_requirements)): ?>
                                        <div class="mt-3">
                                            <strong>Special Requirements:</strong><br>
                                            <span class="text-muted"><?php echo nl2br(htmlspecialchars($booking->special_requirements)); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- Summary & Actions -->
                <div class="col-lg-4">
                    <div class="card-modern mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Order Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="summary-item d-flex justify-content-between mb-2">
                                <span>Total Events:</span>
                                <span><?php echo count($bookings); ?></span>
                            </div>
                            <div class="summary-item d-flex justify-content-between mb-2">
                                <span>Total Tickets:</span>
                                <span><?php echo array_sum(array_column($bookings, 'quantity')); ?></span>
                            </div>
                            <hr>
                            <div class="summary-total d-flex justify-content-between mb-4">
                                <strong>Total Amount:</strong>
                                <strong class="text-primary"><?php echo formatCurrency($totalAmount); ?></strong>
                            </div>

                            <div class="payment-status">
                                <div class="alert alert-warning">
                                    <i class="fas fa-clock me-2"></i>
                                    <strong>Payment Pending</strong><br>
                                    <small>You will receive payment instructions via email.</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-modern">
                        <div class="card-body">
                            <h6 class="fw-bold mb-3">What's Next?</h6>
                            <div class="d-grid gap-2">
                                <a href="../user/dashboard.php" class="btn btn-primary-modern">
                                    <i class="fas fa-tachometer-alt me-2"></i>
                                    View My Bookings
                                </a>
                                <a href="../events/" class="btn btn-outline-primary">
                                    <i class="fas fa-calendar-alt me-2"></i>
                                    Browse More Events
                                </a>
                                <button onclick="window.print()" class="btn btn-outline-secondary">
                                    <i class="fas fa-print me-2"></i>
                                    Print Confirmation
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Important Information -->
                    <div class="card-modern mt-3">
                        <div class="card-body">
                            <h6 class="fw-bold mb-3">Important Information</h6>
                            <ul class="list-unstyled small">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Save your booking references for future reference
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Arrive 30 minutes before event start time
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Bring a valid ID for verification
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Contact organizer for any special requirements
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Toast Container -->
    <div class="toast-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadCartCount();

            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(alert => {
                    if (alert.classList.contains('alert-success')) {
                        alert.style.transition = 'opacity 0.5s';
                        alert.style.opacity = '0';
                        setTimeout(() => alert.remove(), 500);
                    }
                });
            }, 5000);
        });
    </script>
</body>
</html>
