<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../auth/login.php');
}

$pageTitle = 'Booking Confirmation';

// Get booking references from URL
$references = $_GET['references'] ?? '';
$emailSent = $_GET['email_sent'] ?? '0';

if (empty($references)) {
    redirect('../user/dashboard.php');
}

$bookingReferences = explode(',', $references);
$bookings = [];
$totalAmount = 0;

// Get booking details
foreach ($bookingReferences as $reference) {
    $booking = $bookingManager->getBookingByReference(trim($reference));
    if ($booking) {
        $bookings[] = $booking;
        $totalAmount += $booking->total_amount;
    }
}

if (empty($bookings)) {
    redirect('../user/dashboard.php');
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">

    <style>
        body {
            background: var(--secondary-light);
        }

        .confirmation-hero {
            background: var(--primary-gradient);
            color: white;
            padding: var(--space-3xl) 0 var(--space-2xl);
            margin-top: 80px;
            border-radius: 0 0 30px 30px;
            position: relative;
            overflow: hidden;
        }

        .confirmation-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .confirmation-hero .container {
            position: relative;
            z-index: 2;
        }

        .success-animation {
            animation: successPulse 2s ease-in-out infinite;
        }

        @keyframes successPulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .ticket-card {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-medium);
            overflow: hidden;
            position: relative;
            margin-bottom: var(--space-lg);
            border: 2px dashed #e5e7eb;
        }

        .ticket-header {
            background: var(--primary-gradient);
            color: white;
            padding: var(--space-lg);
            position: relative;
        }

        .ticket-header::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            right: 0;
            height: 20px;
            background: radial-gradient(circle at 10px, transparent 10px, white 10px);
            background-size: 20px 20px;
        }

        .ticket-body {
            padding: var(--space-xl);
            background: white;
        }

        .qr-code-container {
            background: var(--secondary-light);
            border-radius: var(--border-radius);
            padding: var(--space-lg);
            text-align: center;
            border: 2px solid #e5e7eb;
        }

        .download-buttons {
            display: flex;
            gap: var(--space-sm);
            flex-wrap: wrap;
        }

        .ticket-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-lg);
            margin: var(--space-lg) 0;
        }

        .info-item {
            text-align: center;
            padding: var(--space-md);
            background: var(--secondary-light);
            border-radius: var(--border-radius);
        }

        .info-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-sm);
            font-size: 1.5rem;
        }

        .confirmation-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-medium);
            margin-top: -50px;
            position: relative;
            z-index: 3;
            overflow: hidden;
        }

        .confirmation-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--space-xs);
            padding: var(--space-sm) var(--space-md);
            border-radius: var(--border-radius-full);
            font-weight: 600;
            font-size: var(--text-sm);
        }

        .status-confirmed {
            background: rgba(16, 185, 129, 0.1);
            color: #065f46;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .print-styles {
            display: none;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            .print-styles {
                display: block;
            }

            body {
                background: white !important;
            }

            .ticket-card {
                page-break-inside: avoid;
                margin-bottom: 2rem;
                border: 2px solid #000;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                ZARA-Events
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../events/">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../events/search.php">Search</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="cart.php">
                            <i class="fas fa-shopping-cart cart-icon"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle" style="display: none;">0</span>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../user/dashboard.php">Dashboard</a></li>
                            <li><a class="dropdown-item" href="../user/profile.php">Profile</a></li>
                            <?php if (isAdmin()): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../admin/">Admin Panel</a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Confirmation Hero Section -->
    <section class="confirmation-hero">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <div class="success-animation mb-4">
                        <i class="fas fa-check-circle fa-5x"></i>
                    </div>
                    <h1 class="display-4 fw-bold mb-3">Booking Confirmed!</h1>
                    <p class="lead mb-0">Your tickets have been successfully reserved. Get ready for an amazing experience!</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="py-4">
        <div class="container">
            <!-- Confirmation Status -->
            <div class="row justify-content-center mb-4">
                <div class="col-lg-8">
                    <div class="confirmation-card p-4">
                        <div class="text-center">
                            <div class="status-badge status-confirmed mb-3">
                                <i class="fas fa-check-circle"></i>
                                Payment Confirmed
                            </div>

                            <?php if ($emailSent === '1'): ?>
                                <div class="alert alert-success border-0">
                                    <i class="fas fa-envelope me-2"></i>
                                    Confirmation email has been sent to your email address.
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning border-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Booking confirmed but email could not be sent. Please save your booking references below.
                                </div>
                            <?php endif; ?>

                            <div class="ticket-info">
                                <div class="info-item">
                                    <div class="info-icon">
                                        <i class="fas fa-ticket-alt"></i>
                                    </div>
                                    <h6 class="fw-bold">Total Events</h6>
                                    <p class="mb-0"><?php echo count($bookings); ?></p>
                                </div>
                                <div class="info-item">
                                    <div class="info-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <h6 class="fw-bold">Total Tickets</h6>
                                    <p class="mb-0"><?php echo array_sum(array_column($bookings, 'quantity')); ?></p>
                                </div>
                                <div class="info-item">
                                    <div class="info-icon">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </div>
                                    <h6 class="fw-bold">Total Amount</h6>
                                    <p class="mb-0"><?php echo formatCurrency($totalAmount); ?></p>
                                </div>
                            </div>

                            <div class="download-buttons justify-content-center mt-4">
                                <button onclick="downloadAllTickets()" class="btn btn-primary-enhanced btn-lg">
                                    <i class="fas fa-download me-2"></i>
                                    Download All Tickets
                                </button>
                                <button onclick="window.print()" class="btn btn-secondary-enhanced btn-lg">
                                    <i class="fas fa-print me-2"></i>
                                    Print Tickets
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Digital Tickets -->
            <div class="row">
                <div class="col-12">
                    <h3 class="fw-bold mb-4 text-center">
                        <i class="fas fa-ticket-alt me-2"></i>
                        Your Digital Tickets
                    </h3>
                </div>
            </div>

            <?php foreach ($bookings as $index => $booking): ?>
                <div class="row justify-content-center mb-4">
                    <div class="col-lg-10">
                        <div class="ticket-card" id="ticket-<?php echo $booking->id; ?>">
                            <!-- Ticket Header -->
                            <div class="ticket-header">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h4 class="fw-bold mb-2"><?php echo htmlspecialchars($booking->title); ?></h4>
                                        <p class="mb-0 opacity-75">
                                            <i class="fas fa-calendar-alt me-2"></i>
                                            <?php echo formatDate($booking->event_date); ?> at <?php echo formatTime($booking->event_time); ?>
                                        </p>
                                    </div>
                                    <div class="col-md-4 text-md-end">
                                        <div class="fw-bold fs-5">ZARA-Events</div>
                                        <small class="opacity-75">Digital Ticket</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Ticket Body -->
                            <div class="ticket-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="row mb-4">
                                            <div class="col-sm-6 mb-3">
                                                <h6 class="text-muted mb-1">Attendee Name</h6>
                                                <p class="fw-bold mb-0"><?php echo htmlspecialchars($booking->attendee_name); ?></p>
                                            </div>
                                            <div class="col-sm-6 mb-3">
                                                <h6 class="text-muted mb-1">Email</h6>
                                                <p class="fw-bold mb-0"><?php echo htmlspecialchars($booking->attendee_email); ?></p>
                                            </div>
                                            <div class="col-sm-6 mb-3">
                                                <h6 class="text-muted mb-1">Venue</h6>
                                                <p class="fw-bold mb-0"><?php echo htmlspecialchars($booking->venue); ?></p>
                                            </div>
                                            <div class="col-sm-6 mb-3">
                                                <h6 class="text-muted mb-1">Location</h6>
                                                <p class="fw-bold mb-0"><?php echo htmlspecialchars($booking->location); ?></p>
                                            </div>
                                            <div class="col-sm-6 mb-3">
                                                <h6 class="text-muted mb-1">Quantity</h6>
                                                <p class="fw-bold mb-0"><?php echo $booking->quantity; ?> ticket<?php echo $booking->quantity > 1 ? 's' : ''; ?></p>
                                            </div>
                                            <div class="col-sm-6 mb-3">
                                                <h6 class="text-muted mb-1">Amount Paid</h6>
                                                <p class="fw-bold mb-0 text-success"><?php echo formatCurrency($booking->total_amount); ?></p>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <h6 class="text-muted mb-1">Booking Reference</h6>
                                            <div class="d-flex align-items-center gap-2">
                                                <span class="badge bg-primary fs-6 px-3 py-2"><?php echo $booking->booking_reference; ?></span>
                                                <button onclick="copyToClipboard('<?php echo $booking->booking_reference; ?>')"
                                                        class="btn btn-outline-secondary btn-sm">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <?php if (!empty($booking->special_requirements)): ?>
                                            <div class="mb-3">
                                                <h6 class="text-muted mb-1">Special Requirements</h6>
                                                <p class="mb-0"><?php echo nl2br(htmlspecialchars($booking->special_requirements)); ?></p>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="qr-code-container">
                                            <div id="qr-<?php echo $booking->id; ?>" class="mb-3"></div>
                                            <h6 class="fw-bold mb-2">Scan for Entry</h6>
                                            <small class="text-muted">Show this QR code at the venue entrance</small>
                                        </div>

                                        <div class="download-buttons mt-3 justify-content-center">
                                            <button onclick="downloadTicket('<?php echo $booking->id; ?>')"
                                                    class="btn btn-primary-enhanced btn-sm w-100 mb-2">
                                                <i class="fas fa-download me-1"></i>
                                                Download Ticket
                                            </button>
                                            <button onclick="shareTicket('<?php echo $booking->booking_reference; ?>')"
                                                    class="btn btn-outline-secondary btn-sm w-100">
                                                <i class="fas fa-share me-1"></i>
                                                Share
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>

            <!-- Important Information & Actions -->
            <div class="row justify-content-center mt-5">
                <div class="col-lg-8">
                    <div class="confirmation-card p-4">
                        <h5 class="fw-bold mb-4 text-center">
                            <i class="fas fa-info-circle me-2"></i>
                            Important Information
                        </h5>

                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li class="mb-3 d-flex align-items-start">
                                        <i class="fas fa-check-circle text-success me-3 mt-1"></i>
                                        <div>
                                            <strong>Save Your Tickets</strong><br>
                                            <small class="text-muted">Download or save screenshots of your tickets</small>
                                        </div>
                                    </li>
                                    <li class="mb-3 d-flex align-items-start">
                                        <i class="fas fa-clock text-success me-3 mt-1"></i>
                                        <div>
                                            <strong>Arrive Early</strong><br>
                                            <small class="text-muted">Arrive 30 minutes before event start time</small>
                                        </div>
                                    </li>
                                    <li class="mb-3 d-flex align-items-start">
                                        <i class="fas fa-id-card text-success me-3 mt-1"></i>
                                        <div>
                                            <strong>Bring Valid ID</strong><br>
                                            <small class="text-muted">Required for verification at venue entrance</small>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li class="mb-3 d-flex align-items-start">
                                        <i class="fas fa-qrcode text-success me-3 mt-1"></i>
                                        <div>
                                            <strong>QR Code Entry</strong><br>
                                            <small class="text-muted">Show QR code on your phone or printed ticket</small>
                                        </div>
                                    </li>
                                    <li class="mb-3 d-flex align-items-start">
                                        <i class="fas fa-phone text-success me-3 mt-1"></i>
                                        <div>
                                            <strong>Contact Support</strong><br>
                                            <small class="text-muted">Call +237 123 456 789 for assistance</small>
                                        </div>
                                    </li>
                                    <li class="mb-3 d-flex align-items-start">
                                        <i class="fas fa-envelope text-success me-3 mt-1"></i>
                                        <div>
                                            <strong>Email Confirmation</strong><br>
                                            <small class="text-muted">Check your email for detailed instructions</small>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="text-center mt-4 pt-4 border-top">
                            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                <a href="../user/dashboard.php" class="btn btn-primary-enhanced btn-lg">
                                    <i class="fas fa-tachometer-alt me-2"></i>
                                    View Dashboard
                                </a>
                                <a href="../events/" class="btn btn-secondary-enhanced btn-lg">
                                    <i class="fas fa-calendar-alt me-2"></i>
                                    Browse More Events
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Toast Container -->
    <div class="toast-container"></div>

    <!-- QR Code Library -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

    <!-- jsPDF for ticket download -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <!-- html2canvas for ticket capture -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadCartCount();
            generateQRCodes();

            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(alert => {
                    if (alert.classList.contains('alert-success')) {
                        alert.style.transition = 'opacity 0.5s';
                        alert.style.opacity = '0';
                        setTimeout(() => alert.remove(), 500);
                    }
                });
            }, 5000);
        });

        // Generate QR codes for all tickets
        function generateQRCodes() {
            <?php foreach ($bookings as $booking): ?>
                const qrData<?php echo $booking->id; ?> = {
                    booking_reference: '<?php echo $booking->booking_reference; ?>',
                    event_title: '<?php echo addslashes($booking->title); ?>',
                    attendee_name: '<?php echo addslashes($booking->attendee_name); ?>',
                    event_date: '<?php echo $booking->event_date; ?>',
                    event_time: '<?php echo $booking->event_time; ?>',
                    venue: '<?php echo addslashes($booking->venue); ?>',
                    quantity: <?php echo $booking->quantity; ?>,
                    verification_url: '<?php echo SITE_URL; ?>/verify-ticket.php?ref=<?php echo $booking->booking_reference; ?>'
                };

                QRCode.toCanvas(document.getElementById('qr-<?php echo $booking->id; ?>'), JSON.stringify(qrData<?php echo $booking->id; ?>), {
                    width: 150,
                    height: 150,
                    colorDark: '#408681',
                    colorLight: '#ffffff',
                    margin: 2
                }, function (error) {
                    if (error) console.error('QR Code generation failed:', error);
                });
            <?php endforeach; ?>
        }

        // Copy booking reference to clipboard
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                showToast('Booking reference copied to clipboard!', 'success');
            }, function(err) {
                console.error('Could not copy text: ', err);
                showToast('Failed to copy to clipboard', 'error');
            });
        }

        // Download individual ticket as PDF
        function downloadTicket(ticketId) {
            const ticketElement = document.getElementById('ticket-' + ticketId);

            // Show loading
            showToast('Generating ticket PDF...', 'info');

            html2canvas(ticketElement, {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff'
            }).then(canvas => {
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF('p', 'mm', 'a4');

                const imgData = canvas.toDataURL('image/png');
                const imgWidth = 190;
                const pageHeight = 295;
                const imgHeight = (canvas.height * imgWidth) / canvas.width;
                let heightLeft = imgHeight;

                let position = 10;

                pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;

                while (heightLeft >= 0) {
                    position = heightLeft - imgHeight + 10;
                    pdf.addPage();
                    pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight);
                    heightLeft -= pageHeight;
                }

                pdf.save('ZARA-Events-Ticket-' + ticketId + '.pdf');
                showToast('Ticket downloaded successfully!', 'success');
            }).catch(error => {
                console.error('Error generating PDF:', error);
                showToast('Failed to generate PDF', 'error');
            });
        }

        // Download all tickets as a single PDF
        function downloadAllTickets() {
            showToast('Generating all tickets PDF...', 'info');

            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF('p', 'mm', 'a4');
            let currentPage = 0;

            const tickets = document.querySelectorAll('[id^="ticket-"]');
            const promises = Array.from(tickets).map((ticket, index) => {
                return html2canvas(ticket, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff'
                }).then(canvas => {
                    return { canvas, index };
                });
            });

            Promise.all(promises).then(results => {
                results.forEach(({ canvas, index }) => {
                    if (index > 0) {
                        pdf.addPage();
                    }

                    const imgData = canvas.toDataURL('image/png');
                    const imgWidth = 190;
                    const imgHeight = (canvas.height * imgWidth) / canvas.width;

                    pdf.addImage(imgData, 'PNG', 10, 10, imgWidth, imgHeight);
                });

                pdf.save('ZARA-Events-All-Tickets.pdf');
                showToast('All tickets downloaded successfully!', 'success');
            }).catch(error => {
                console.error('Error generating PDF:', error);
                showToast('Failed to generate PDF', 'error');
            });
        }

        // Share ticket
        function shareTicket(bookingRef) {
            const shareData = {
                title: 'ZARA-Events Ticket',
                text: 'Check out my event ticket!',
                url: window.location.href
            };

            if (navigator.share) {
                navigator.share(shareData).then(() => {
                    showToast('Ticket shared successfully!', 'success');
                }).catch(err => {
                    console.error('Error sharing:', err);
                    fallbackShare(bookingRef);
                });
            } else {
                fallbackShare(bookingRef);
            }
        }

        // Fallback share method
        function fallbackShare(bookingRef) {
            const shareText = `Check out my ZARA-Events ticket! Booking Reference: ${bookingRef}`;
            copyToClipboard(shareText);
            showToast('Ticket details copied to clipboard for sharing!', 'info');
        }

        // Print optimization
        window.addEventListener('beforeprint', function() {
            document.body.classList.add('printing');
        });

        window.addEventListener('afterprint', function() {
            document.body.classList.remove('printing');
        });
    </script>
</body>
</html>
