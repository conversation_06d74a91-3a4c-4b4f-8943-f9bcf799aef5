<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../auth/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

$pageTitle = 'Checkout';
$userId = $_SESSION['user_id'];
$user = $userManager->getUserById($userId);

// Handle quick booking from event details
$quickBookEventId = $_GET['event_id'] ?? 0;
$quickBookQuantity = (int)($_GET['quantity'] ?? 1);

if ($quickBookEventId) {
    // Add to cart for quick booking
    $event = $eventManager->getEventById($quickBookEventId);
    if ($event && $event->available_tickets >= $quickBookQuantity) {
        $cartManager->addToCart($userId, $quickBookEventId, $quickBookQuantity);
    }
}

// Get cart items
$cartItems = $cartManager->getCartItems($userId);
$cartTotal = $cartManager->getCartTotal($userId);
$cartCount = $cartManager->getCartCount($userId);

// Redirect if cart is empty
if (empty($cartItems)) {
    setFlashMessage('error', 'Your cart is empty. Please add some events before checkout.');
    redirect('cart.php');
}

// Handle checkout form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $attendeeName = trim($_POST['attendee_name'] ?? '');
    $attendeeEmail = trim($_POST['attendee_email'] ?? '');
    $attendeePhone = trim($_POST['attendee_phone'] ?? '');
    $specialRequirements = trim($_POST['special_requirements'] ?? '');
    $paymentMethod = $_POST['payment_method'] ?? '';

    // Validate input
    $errors = [];
    if (empty($attendeeName)) $errors[] = 'Attendee name is required';
    if (empty($attendeeEmail)) $errors[] = 'Attendee email is required';
    if (!filter_var($attendeeEmail, FILTER_VALIDATE_EMAIL)) $errors[] = 'Valid email is required';
    if (empty($attendeePhone)) $errors[] = 'Phone number is required';
    if (empty($paymentMethod)) $errors[] = 'Payment method is required';

    if (empty($errors)) {
        try {
            $bookingReferences = [];
            $totalAmount = 0;

            // Create bookings for each cart item (each booking handles its own transaction)
            foreach ($cartItems as $item) {
                $attendeeData = [
                    'name' => $attendeeName,
                    'email' => $attendeeEmail,
                    'phone' => $attendeePhone,
                    'special_requirements' => $specialRequirements
                ];

                $bookingId = $bookingManager->createBooking($userId, $item->event_id, $item->quantity, $attendeeData);

                if (!$bookingId) {
                    throw new Exception('Failed to create booking for ' . $item->title);
                }

                // Get booking reference
                $db->query('SELECT booking_reference FROM bookings WHERE id = :id');
                $db->bind(':id', $bookingId);
                $booking = $db->single();

                if ($booking) {
                    $bookingReferences[] = $booking->booking_reference;
                    $totalAmount += $item->price * $item->quantity;
                }
            }

            // Clear cart after successful booking
            $cartManager->clearCart($userId);

            // Send confirmation email
            try {
                $emailSent = sendBookingConfirmationEmail($attendeeEmail, $attendeeName, $bookingReferences, $totalAmount);
            } catch (Exception $e) {
                error_log('Email sending failed: ' . $e->getMessage());
                $emailSent = false;
            }

            // Redirect to confirmation page
            $references = implode(',', $bookingReferences);
            redirect('confirmation.php?references=' . urlencode($references) . '&email_sent=' . ($emailSent ? '1' : '0'));

        } catch (Exception $e) {
            setFlashMessage('error', 'Booking failed: ' . $e->getMessage());
        }
    } else {
        setFlashMessage('error', implode('<br>', $errors));
    }
}

$flashMessage = getFlashMessage();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">

    <style>
        body {
            background: var(--secondary-light);
        }

        .checkout-progress {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 2rem 0;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(64, 134, 129, 0.1);
        }

        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            min-width: 120px;
        }

        .step-number {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .progress-step.completed .step-number {
            background: var(--primary-gradient);
            color: white;
        }

        .progress-step.active .step-number {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 0 0 4px rgba(64, 134, 129, 0.2);
        }

        .step-label {
            font-size: 0.9rem;
            font-weight: 500;
            color: #6c757d;
        }

        .progress-step.completed .step-label,
        .progress-step.active .step-label {
            color: var(--primary-color);
        }

        .progress-line {
            height: 2px;
            background: #e9ecef;
            flex: 1;
            margin: 0 1rem;
            margin-top: -25px;
            z-index: -1;
        }

        .progress-line.completed {
            background: var(--primary-gradient);
        }

        .checkout-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 60px rgba(64, 134, 129, 0.1);
            overflow: hidden;
            position: relative;
            margin-bottom: 2rem;
        }

        .checkout-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .section-header {
            background: var(--secondary-light);
            padding: 1.5rem;
            border-bottom: 1px solid rgba(64, 134, 129, 0.1);
        }

        .section-title {
            color: var(--primary-color);
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .order-item {
            background: var(--secondary-light);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid rgba(64, 134, 129, 0.1);
        }

        .payment-method-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .payment-method-card:hover {
            border-color: var(--primary-color);
            background: var(--secondary-light);
        }

        .payment-method-card.selected {
            border-color: var(--primary-color);
            background: var(--secondary-light);
            box-shadow: 0 0 0 2px rgba(64, 134, 129, 0.1);
        }

        .order-summary-card {
            background: var(--primary-gradient);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            position: sticky;
            top: 100px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.75rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .summary-total {
            font-size: 1.25rem;
            font-weight: bold;
            padding-top: 1rem;
            border-top: 2px solid rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                ZARA-Events
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../events/">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../events/search.php">Search</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="cart.php">
                            <i class="fas fa-shopping-cart cart-icon"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle"
                                  <?php echo $cartCount > 0 ? '' : 'style="display: none;"'; ?>>
                                <?php echo $cartCount; ?>
                            </span>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['first_name'] ?? 'User'); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../user/dashboard.php">Dashboard</a></li>
                            <li><a class="dropdown-item" href="../user/profile.php">Profile</a></li>
                            <?php if (isAdmin()): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../admin/">Admin Panel</a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Checkout Progress -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="checkout-progress">
                        <div class="progress-step completed">
                            <div class="step-number">1</div>
                            <div class="step-label">Cart</div>
                        </div>
                        <div class="progress-line completed"></div>
                        <div class="progress-step active">
                            <div class="step-number">2</div>
                            <div class="step-label">Checkout</div>
                        </div>
                        <div class="progress-line"></div>
                        <div class="progress-step">
                            <div class="step-number">3</div>
                            <div class="step-label">Confirmation</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h2>
                        <i class="fas fa-credit-card me-2"></i>
                        Checkout
                    </h2>
                </div>
            </div>

            <?php if ($flashMessage): ?>
                <div class="alert alert-<?php echo $flashMessage['type'] === 'error' ? 'danger' : $flashMessage['type']; ?> alert-dismissible fade show">
                    <?php echo $flashMessage['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <form method="POST" action="">
                <div class="row">
                    <!-- Checkout Form -->
                    <div class="col-lg-8 mb-4">
                        <!-- Attendee Information -->
                        <div class="checkout-section">
                            <div class="section-header">
                                <h5 class="section-title">
                                    <i class="fas fa-user"></i>
                                    Attendee Information
                                </h5>
                            </div>
                            <div class="p-4">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="attendee_name" class="form-label">Full Name *</label>
                                        <input type="text" class="form-control form-control-modern" id="attendee_name"
                                               name="attendee_name" value="<?php echo htmlspecialchars($user->first_name . ' ' . $user->last_name); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="attendee_email" class="form-label">Email Address *</label>
                                        <input type="email" class="form-control form-control-modern" id="attendee_email"
                                               name="attendee_email" value="<?php echo htmlspecialchars($user->email); ?>" required>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="attendee_phone" class="form-label">Phone Number *</label>
                                        <input type="tel" class="form-control form-control-modern" id="attendee_phone"
                                               name="attendee_phone" value="<?php echo htmlspecialchars($user->phone); ?>" required>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="special_requirements" class="form-label">Special Requirements</label>
                                    <textarea class="form-control form-control-modern" id="special_requirements"
                                              name="special_requirements" rows="3"
                                              placeholder="Any special dietary requirements, accessibility needs, etc."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Method -->
                        <div class="checkout-section">
                            <div class="section-header">
                                <h5 class="section-title">
                                    <i class="fas fa-credit-card"></i>
                                    Payment Method
                                </h5>
                            </div>
                            <div class="p-4">
                                <div class="payment-methods">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="radio" name="payment_method" id="mobile_money" value="mobile_money" checked>
                                        <label class="form-check-label" for="mobile_money">
                                            <i class="fas fa-mobile-alt me-2"></i>
                                            Mobile Money (MTN, Orange)
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="radio" name="payment_method" id="bank_transfer" value="bank_transfer">
                                        <label class="form-check-label" for="bank_transfer">
                                            <i class="fas fa-university me-2"></i>
                                            Bank Transfer
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="payment_method" id="cash" value="cash">
                                        <label class="form-check-label" for="cash">
                                            <i class="fas fa-money-bill me-2"></i>
                                            Cash Payment (Pay at venue)
                                        </label>
                                    </div>
                                </div>

                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Note:</strong> You will receive payment instructions via email after confirming your booking.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="col-lg-4">
                        <div class="order-summary-card">
                            <h5 class="mb-4">
                                <i class="fas fa-receipt me-2"></i>
                                Order Summary
                            </h5>
                            <!-- Cart Items -->
                            <?php foreach ($cartItems as $item): ?>
                                <div class="summary-item">
                                    <div class="d-flex align-items-center flex-grow-1">
                                        <img src="<?php echo $item->image_url ?: 'https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&auto=format&fit=crop&w=50&q=80'; ?>"
                                             alt="<?php echo htmlspecialchars($item->title); ?>"
                                             class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1 text-white"><?php echo htmlspecialchars($item->title); ?></h6>
                                            <small class="text-white-50">
                                                <?php echo formatDate($item->event_date); ?><br>
                                                Qty: <?php echo $item->quantity; ?> × <?php echo formatCurrency($item->price); ?>
                                            </small>
                                        </div>
                                    </div>
                                    <div class="fw-bold">
                                        <?php echo formatCurrency($item->price * $item->quantity); ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>

                            <!-- Total -->
                            <div class="summary-total d-flex justify-content-between">
                                <span>Total Amount:</span>
                                <span><?php echo formatCurrency($cartTotal); ?></span>
                            </div>

                            <!-- Action Buttons -->
                            <div class="d-grid gap-2 mt-4">
                                <button type="submit" class="btn btn-light btn-lg">
                                    <i class="fas fa-check me-2"></i>
                                    Confirm Booking
                                </button>
                                <a href="cart.php" class="btn btn-outline-light">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    Back to Cart
                                </a>
                            </div>

                            <!-- Security Info -->
                            <div class="mt-4 pt-3 text-center" style="border-top: 1px solid rgba(255, 255, 255, 0.2);">
                                <small class="text-white-50">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    Your information is secure and encrypted
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>
</body>
</html>
