<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../auth/login.php');
}

$pageTitle = 'Payment Processing';
$userId = $_SESSION['user_id'];

// Get parameters
$bookingReferences = explode(',', $_GET['references'] ?? '');
$paymentReferences = explode(',', $_GET['payments'] ?? '');
$emailSent = $_GET['email_sent'] ?? '0';

// Validate parameters
if (empty($bookingReferences[0]) || empty($paymentReferences[0])) {
    redirect('cart.php');
}

// Get payment details
$payments = [];
$totalAmount = 0;
$paymentMethod = '';

foreach ($paymentReferences as $paymentRef) {
    if (!empty($paymentRef)) {
        $payment = $paymentManager->getPaymentByReference($paymentRef);
        if ($payment && $payment->user_id == $userId) {
            $payments[] = $payment;
            $totalAmount += $payment->amount;
            $paymentMethod = $payment->payment_method;
        }
    }
}

if (empty($payments)) {
    redirect('cart.php');
}

// Get the first payment for details (assuming all payments use same method)
$mainPayment = $payments[0];
$paymentDetails = json_decode($mainPayment->payment_details, true);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">

    <style>
        body {
            background: var(--secondary-light);
        }

        .payment-hero {
            background: var(--primary-gradient);
            color: white;
            padding: var(--space-3xl) 0 var(--space-2xl);
            margin-top: 80px;
            border-radius: 0 0 30px 30px;
            position: relative;
            overflow: hidden;
        }

        .payment-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .payment-hero .container {
            position: relative;
            z-index: 2;
        }

        .payment-status-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-medium);
            margin-top: -50px;
            position: relative;
            z-index: 3;
            overflow: hidden;
        }

        .payment-status-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .processing-animation {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-sm);
            margin: var(--space-xl) 0;
        }

        .processing-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--primary-color);
            animation: processing 1.5s infinite ease-in-out;
        }

        .processing-dot:nth-child(2) {
            animation-delay: 0.3s;
        }

        .processing-dot:nth-child(3) {
            animation-delay: 0.6s;
        }

        @keyframes processing {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1.2);
                opacity: 1;
            }
        }

        .payment-method-info {
            background: var(--secondary-light);
            border-radius: var(--border-radius);
            padding: var(--space-lg);
            border-left: 4px solid var(--primary-color);
        }

        .status-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin: 0 auto var(--space-lg);
        }

        .status-processing {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            animation: pulse 2s infinite;
        }

        .status-success {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .status-failed {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.8;
            }
        }

        .countdown-timer {
            font-size: var(--text-2xl);
            font-weight: 700;
            color: var(--primary-color);
            text-align: center;
            margin: var(--space-lg) 0;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                ZARA-Events
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../user/dashboard.php">Dashboard</a>
                <a class="nav-link" href="../auth/logout.php">Logout</a>
            </div>
        </div>
    </nav>

    <!-- Payment Hero Section -->
    <section class="payment-hero">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="display-4 fw-bold mb-3">
                        <i class="fas fa-credit-card me-3"></i>
                        Payment Processing
                    </h1>
                    <p class="lead mb-0">Please wait while we process your payment securely</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Payment Status Section -->
    <section class="py-4">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="payment-status-card p-5">
                        <div id="processingState" class="text-center">
                            <div class="status-icon status-processing">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                            <h3 class="fw-bold mb-3">Processing Your Payment</h3>
                            <p class="text-muted mb-4">Please do not close this window. We're securely processing your payment...</p>
                            
                            <div class="processing-animation">
                                <div class="processing-dot"></div>
                                <div class="processing-dot"></div>
                                <div class="processing-dot"></div>
                            </div>

                            <div class="countdown-timer" id="countdownTimer">
                                Estimated time: <span id="timeRemaining">5</span> seconds
                            </div>
                        </div>

                        <div id="successState" class="text-center" style="display: none;">
                            <div class="status-icon status-success">
                                <i class="fas fa-check"></i>
                            </div>
                            <h3 class="fw-bold mb-3 text-success">Payment Successful!</h3>
                            <p class="text-muted mb-4">Your payment has been processed successfully. Your booking is now confirmed.</p>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                <a href="confirmation.php?references=<?php echo urlencode(implode(',', $bookingReferences)); ?>&email_sent=<?php echo $emailSent; ?>" 
                                   class="btn btn-primary-enhanced btn-lg">
                                    <i class="fas fa-receipt me-2"></i>
                                    View Confirmation
                                </a>
                                <a href="../user/dashboard.php" class="btn btn-secondary-enhanced btn-lg">
                                    <i class="fas fa-tachometer-alt me-2"></i>
                                    Go to Dashboard
                                </a>
                            </div>
                        </div>

                        <div id="failedState" class="text-center" style="display: none;">
                            <div class="status-icon status-failed">
                                <i class="fas fa-times"></i>
                            </div>
                            <h3 class="fw-bold mb-3 text-danger">Payment Failed</h3>
                            <p class="text-muted mb-4">We couldn't process your payment. Please try again or contact support.</p>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                <a href="checkout.php" class="btn btn-primary-enhanced btn-lg">
                                    <i class="fas fa-redo me-2"></i>
                                    Try Again
                                </a>
                                <a href="../user/dashboard.php" class="btn btn-secondary-enhanced btn-lg">
                                    <i class="fas fa-tachometer-alt me-2"></i>
                                    Go to Dashboard
                                </a>
                            </div>
                        </div>

                        <!-- Payment Summary -->
                        <div class="payment-method-info mt-4">
                            <h6 class="fw-bold mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                Payment Summary
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-2"><strong>Payment Method:</strong> <?php echo ucwords(str_replace('_', ' ', $paymentMethod)); ?></p>
                                    <p class="mb-2"><strong>Total Amount:</strong> <?php echo formatCurrency($totalAmount); ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-2"><strong>Bookings:</strong> <?php echo count($bookingReferences); ?> event(s)</p>
                                    <p class="mb-2"><strong>Payment Reference:</strong> <?php echo $mainPayment->payment_reference; ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Toast Container -->
    <div class="toast-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <script>
        // Simulate payment processing
        let timeRemaining = 5;
        const countdownElement = document.getElementById('timeRemaining');
        const processingState = document.getElementById('processingState');
        const successState = document.getElementById('successState');
        const failedState = document.getElementById('failedState');

        // Countdown timer
        const countdown = setInterval(() => {
            timeRemaining--;
            countdownElement.textContent = timeRemaining;
            
            if (timeRemaining <= 0) {
                clearInterval(countdown);
                processPayment();
            }
        }, 1000);

        function processPayment() {
            // Simulate payment processing result (90% success rate)
            const isSuccessful = Math.random() > 0.1;
            
            setTimeout(() => {
                processingState.style.display = 'none';
                
                if (isSuccessful) {
                    successState.style.display = 'block';
                    showToast('Payment processed successfully!', 'success');
                } else {
                    failedState.style.display = 'block';
                    showToast('Payment processing failed. Please try again.', 'error');
                }
            }, 1000);
        }

        // Prevent page refresh during processing
        window.addEventListener('beforeunload', function(e) {
            if (processingState.style.display !== 'none') {
                e.preventDefault();
                e.returnValue = 'Payment is being processed. Are you sure you want to leave?';
            }
        });
    </script>
</body>
</html>
