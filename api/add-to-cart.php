<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Please login to add items to cart'
    ]);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $eventId = (int)($input['event_id'] ?? 0);
    $quantity = (int)($input['quantity'] ?? 1);
    $userId = $_SESSION['user_id'];
    
    // Validate input
    if ($eventId <= 0) {
        throw new Exception('Invalid event ID');
    }
    
    if ($quantity <= 0 || $quantity > 10) {
        throw new Exception('Invalid quantity. Must be between 1 and 10');
    }
    
    // Check if event exists and is available
    $db->query('SELECT * FROM events WHERE id = :id AND status = "active"');
    $db->bind(':id', $eventId);
    $event = $db->single();
    
    if (!$event) {
        throw new Exception('Event not found or not available');
    }
    
    if ($event->available_tickets < $quantity) {
        throw new Exception('Not enough tickets available');
    }
    
    // Add to cart
    if ($cartManager->addToCart($userId, $eventId, $quantity)) {
        $cartCount = $cartManager->getCartCount($userId);
        
        echo json_encode([
            'success' => true,
            'message' => 'Event added to cart successfully',
            'cart_count' => $cartCount,
            'event_title' => $event->title
        ]);
    } else {
        throw new Exception('Failed to add event to cart');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
