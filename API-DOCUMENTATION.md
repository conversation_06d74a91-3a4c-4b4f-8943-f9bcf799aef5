# Event Booking System - Backend API Documentation

## Overview
This is a backend-only Event Booking System with no frontend UI. All interactions are done through REST API endpoints that return JSON responses.

## Base URL
```
http://localhost:8000
```

## Authentication
Some endpoints require admin authentication. Use the login endpoint first to establish a session.

## API Endpoints

### 1. API Information
**GET /** or **GET /api**
- Returns API documentation and available endpoints
- No authentication required

### 2. Events Management

#### Get All Events
**GET /api/events**
- Returns all active events
- No authentication required
- Response: `{"success": true, "data": [...]}`

#### Create Event (Admin Only)
**POST /api/events**
- Creates a new event
- Requires admin authentication
- Request body: JSON with event data
- Response: `{"success": true, "message": "Event created"}`

### 3. Bookings Management

#### Get All Bookings (Admin Only)
**GET /api/bookings**
- Returns all bookings
- Requires admin authentication
- Response: `{"success": true, "data": [...]}`

#### Create Booking
**POST /api/bookings**
- Creates a new booking
- Request body: JSON with booking data
- Response: `{"success": true, "booking_reference": "BK20240101ABC123"}`

### 4. User Management

#### Get All Users (Admin Only)
**GET /api/users**
- Returns all users (excluding passwords)
- Requires admin authentication
- Response: `{"success": true, "data": [...]}`

### 5. Authentication

#### Login
**POST /api/auth/login**
- Authenticates a user and creates session
- Request body: `{"username": "admin", "password": "admin123"}`
- Response: `{"success": true, "message": "Login successful"}`

#### Register
**POST /api/auth/register**
- Registers a new user
- Request body: JSON with user data
- Response: `{"success": true, "message": "Registration successful"}`

### 6. Statistics (Admin Only)

#### Get System Stats
**GET /api/stats**
- Returns system statistics
- Requires admin authentication
- Response: `{"success": true, "data": {...}}`

## Example Usage

### Login as Admin
```bash
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

### Get All Events
```bash
curl http://localhost:8000/api/events
```

### Get System Statistics (after login)
```bash
curl http://localhost:8000/api/stats
```

## Error Responses
All errors return appropriate HTTP status codes with JSON error messages:
```json
{
  "error": "Error description"
}
```

## Database
- MySQL database running on Docker (localhost:3307)
- Contains sample events, users, and bookings
- Admin user: username `admin`, password `admin123`

## Features Removed
- All frontend UI pages
- HTML templates and forms
- CSS and JavaScript assets
- Image galleries
- User dashboards
- Admin panels

## What Remains
- Complete backend functionality
- Database operations
- User authentication
- Event management
- Booking system
- Email functionality
- API endpoints only
