# Online Event Booking System

A comprehensive web-based event booking system built with PHP, MySQL, HTML, CSS, Bootstrap, and JavaScript. This system allows users to browse, search, and book tickets for various events while providing administrators with tools to manage events, bookings, and generate reports.

## 🚀 Features

### User Features (5 marks each)
- **User Authentication**: Complete registration, login, and session management system
- **Event Listings**: Browse events with detailed information, images, and pricing
- **Search Functionality**: Advanced search with filters by name, location, date, and category
- **Event Details**: Comprehensive event pages with venue information and booking options
- **Booking Cart**: Add multiple events to cart with quantity management (10 marks)
- **Checkout Process**: Secure checkout with attendee information and payment simulation (5 marks)

### Advanced Features (10+ marks each)
- **Booking History**: Complete user dashboard with past and upcoming bookings (10 marks)
- **Admin Panel**: Full administrative interface for event and booking management (15 marks)

### Technical Implementation
- **Responsive Design**: Mobile-friendly interface using Bootstrap 5
- **Security**: CSRF protection, input sanitization, and secure session management
- **Database**: Well-structured MySQL database with proper relationships
- **User Experience**: Intuitive navigation, real-time cart updates, and interactive elements

## 📋 Requirements

- PHP 8.0 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- Modern web browser

## 🛠️ Installation

### 1. Database Setup
```sql
-- Import the database schema
mysql -u username -p < database/schema.sql
```

### 2. Configuration
Update the database configuration in `includes/config.php`:
```php
define('DB_HOST', 'localhost');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_NAME', 'event_booking_system');
```

### 3. File Permissions
Ensure the web server has read/write permissions to:
- `assets/images/` directory for event images
- Session storage directory

### 4. Web Server Setup
Point your web server document root to the project directory.

## 🎯 Usage

### For Users
1. **Registration**: Create an account with personal details
2. **Browse Events**: View available events on the homepage or search page
3. **Event Details**: Click on any event to view detailed information
4. **Add to Cart**: Add desired events to your shopping cart
5. **Checkout**: Complete booking with attendee information
6. **Dashboard**: View booking history and manage profile

### For Administrators
1. **Login**: Use admin credentials (admin/admin123)
2. **Dashboard**: View system statistics and recent activity
3. **Event Management**: Add, edit, or delete events
4. **Booking Management**: View and manage all bookings
5. **Reports**: Generate various reports for analysis

## 📁 Project Structure

```
/
├── index.php                 # Homepage with featured events
├── auth/                     # Authentication system
│   ├── login.php            # User login
│   ├── register.php         # User registration
│   └── logout.php           # Logout functionality
├── events/                   # Event-related pages
│   ├── details.php          # Event details page
│   └── search.php           # Event search and browse
├── booking/                  # Booking system
│   ├── cart.php             # Shopping cart
│   ├── checkout.php         # Checkout process
│   ├── confirmation.php     # Booking confirmation
│   ├── add_to_cart.php      # AJAX cart operations
│   └── get_cart_count.php   # Cart count API
├── user/                     # User dashboard
│   ├── dashboard.php        # User dashboard
│   └── profile.php          # Profile management
├── admin/                    # Admin panel
│   ├── index.php            # Admin dashboard
│   ├── events.php           # Event management
│   ├── bookings.php         # Booking management
│   └── reports.php          # Reports generation
├── includes/                 # Core system files
│   ├── config.php           # Database and site configuration
│   ├── functions.php        # Core functionality classes
│   ├── header.php           # Common header
│   └── footer.php           # Common footer
├── assets/                   # Static assets
│   ├── css/style.css        # Custom styles
│   ├── js/main.js           # JavaScript functionality
│   └── images/              # Image storage
├── database/                 # Database files
│   └── schema.sql           # Database structure and sample data
└── README.md                # This documentation
```

## 🔧 Key Features Implementation

### User Authentication System
- Secure password hashing using PHP's `password_hash()`
- Session-based authentication with timeout
- CSRF token protection for forms
- Role-based access control (user/admin)

### Event Management
- Complete CRUD operations for events
- Image upload and management
- Category-based organization
- Availability tracking

### Booking System
- Shopping cart functionality with session storage
- Real-time availability checking
- Transaction-safe booking process
- Booking reference generation

### Search and Filtering
- Full-text search across event titles and descriptions
- Location-based filtering
- Date range filtering
- Category-based filtering
- Pagination for large result sets

### Admin Panel
- Dashboard with key metrics
- Event management interface
- Booking oversight and management
- User management capabilities
- Report generation tools

## 🎨 Design Features

### Responsive Design
- Mobile-first approach using Bootstrap 5
- Flexible grid system for all screen sizes
- Touch-friendly interface elements
- Optimized images and loading

### User Experience
- Intuitive navigation structure
- Real-time feedback for user actions
- Loading states and progress indicators
- Error handling with user-friendly messages
- Accessibility considerations

### Visual Design
- Modern, clean interface
- Consistent color scheme and typography
- Card-based layout for content organization
- Icons from Font Awesome for visual clarity
- Gradient backgrounds and hover effects

## 🔒 Security Features

- **Input Validation**: All user inputs are sanitized and validated
- **CSRF Protection**: Cross-site request forgery protection on all forms
- **SQL Injection Prevention**: Prepared statements for all database queries
- **Session Security**: Secure session handling with timeout
- **Password Security**: Strong password hashing and validation
- **Access Control**: Role-based permissions for different user types

## 📊 Database Schema

### Core Tables
- **users**: User accounts and profiles
- **events**: Event information and details
- **bookings**: Booking records and status
- **cart**: Temporary cart storage
- **user_sessions**: Session management

### Relationships
- Users can have multiple bookings
- Events can have multiple bookings
- Cart items link users to events
- Foreign key constraints ensure data integrity

## 🚀 Future Enhancements

- **Payment Integration**: Real payment gateway integration
- **Email Notifications**: Automated email confirmations and reminders
- **QR Code Generation**: Digital tickets with QR codes
- **Social Media Integration**: Event sharing and social login
- **Advanced Analytics**: Detailed reporting and analytics
- **Mobile App**: Native mobile application
- **Multi-language Support**: Internationalization
- **Event Reviews**: User reviews and ratings system

## 📝 Testing

### Manual Testing Checklist
- [ ] User registration and login
- [ ] Event browsing and search
- [ ] Cart functionality
- [ ] Checkout process
- [ ] Booking confirmation
- [ ] Admin panel access
- [ ] Event management
- [ ] Booking management
- [ ] Responsive design on various devices

### Test Accounts
- **Admin**: username: `admin`, password: `admin123`
- **User**: Register a new account or create test users

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is created for educational purposes as part of a web development course assignment.

## 👥 Credits

- **Bootstrap 5**: Frontend framework
- **Font Awesome**: Icons
- **jQuery**: JavaScript library
- **PHP**: Server-side scripting
- **MySQL**: Database management

## 📞 Support

For questions or issues related to this project, please refer to the documentation or contact the development team.

---

**Note**: This is a demonstration project for educational purposes. In a production environment, additional security measures, error handling, and performance optimizations would be implemented.
