#!/bin/bash

# Event Booking System - Docker Setup Script
echo "🚀 Setting up Event Booking System with Docker MySQL..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p assets/images/events

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose down

# Start MySQL and phpMyAdmin containers
echo "🏗️  Starting MySQL and phpMyAdmin containers..."
docker-compose up -d

# Wait for MySQL to be ready
echo "⏳ Waiting for MySQL to be ready..."
sleep 20

# Check if containers are running
echo "🔍 Checking container status..."
docker-compose ps

# Display access information
echo ""
echo "✅ Setup complete! Your Event Booking System database is now running:"
echo ""
echo "🗄️  phpMyAdmin: http://localhost:8081"
echo "   - Username: event_user"
echo "   - Password: event_password"
echo ""
echo "👤 Default Admin Login (for the web app):"
echo "   - Username: admin"
echo "   - Password: admin123"
echo ""
echo "👤 Default User Login (for the web app):"
echo "   - Username: testuser"
echo "   - Password: user123"
echo ""
echo "🌐 To start the web application, run:"
echo "   php -S localhost:8000"
echo "   Then visit: http://localhost:8000"
echo ""
echo "🔧 To stop the database: docker-compose down"
echo "🔧 To view logs: docker-compose logs -f"
echo "🔧 To restart: docker-compose restart"
echo ""

# Test database connection
echo "🧪 Testing database connection..."
sleep 5
if docker-compose exec mysql mysql -u event_user -pevent_password -e "USE event_booking_system; SELECT COUNT(*) as event_count FROM events;" 2>/dev/null; then
    echo "✅ Database connection successful!"
    echo ""
    echo "🎉 You can now start the PHP development server:"
    echo "   php -S localhost:8000"
else
    echo "⚠️  Database might still be initializing. Please wait a moment and try again."
fi
