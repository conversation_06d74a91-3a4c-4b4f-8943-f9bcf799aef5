<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in, if not redirect to welcome page
if (!isLoggedIn()) {
    redirect('../welcome.php');
}

$pageTitle = 'Browse Events';

// Get filter parameters
$category = $_GET['category'] ?? '';
$location = $_GET['location'] ?? '';
$search = $_GET['search'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = EVENTS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Get events based on filters
if (!empty($search)) {
    $events = $eventManager->searchEvents($search, $location);
} elseif (!empty($category)) {
    $events = $eventManager->getEventsByCategory($category);
} else {
    $events = $eventManager->getAllEvents($limit, $offset);
}

// Get total count for pagination
$totalEvents = $eventManager->getTotalEventsCount();
$totalPages = ceil($totalEvents / $limit);

// Get unique categories and locations for filters
$db->query('SELECT DISTINCT category FROM events WHERE status = "active" ORDER BY category');
$categories = $db->resultset();

$db->query('SELECT DISTINCT location FROM events WHERE status = "active" ORDER BY location');
$locations = $db->resultset();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                EventHub
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="./">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="search.php">Search</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="../booking/cart.php">
                            <i class="fas fa-shopping-cart cart-icon"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle" style="display: none;">0</span>
                        </a>
                    </li>
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($_SESSION['first_name'] ?? 'User'); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="../user/dashboard.php">Dashboard</a></li>
                                <li><a class="dropdown-item" href="../user/profile.php">Profile</a></li>
                                <?php if (isAdmin()): ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="../admin/">Admin Panel</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../auth/logout.php">Logout</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../auth/login.php">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary-modern ms-2" href="../auth/register.php">Sign Up</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header Section -->
    <section class="py-5 mt-5" style="background: var(--primary-gradient);">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center text-white">
                    <h1 class="display-4 fw-bold mb-3">Discover Amazing Events</h1>
                    <p class="lead">Find the perfect event for your interests and location</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Filters Section -->
    <section class="py-4" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(20px);">
        <div class="container">
            <form method="GET" class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search Events</label>
                    <input type="text" class="form-control form-control-modern"
                           id="search" name="search" value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="Search by title or description">
                </div>

                <div class="col-md-3">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-control form-control-modern" id="category" name="category">
                        <option value="">All Categories</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?php echo htmlspecialchars($cat->category); ?>"
                                    <?php echo $category === $cat->category ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($cat->category); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="col-md-3">
                    <label for="location" class="form-label">Location</label>
                    <select class="form-control form-control-modern" id="location" name="location">
                        <option value="">All Locations</option>
                        <?php foreach ($locations as $loc): ?>
                            <option value="<?php echo htmlspecialchars($loc->location); ?>"
                                    <?php echo $location === $loc->location ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($loc->location); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary-modern w-100">
                        <i class="fas fa-search me-2"></i>
                        Filter Events
                    </button>
                </div>
            </form>
        </div>
    </section>

    <!-- Events Grid -->
    <section class="py-5">
        <div class="container">
            <!-- Results Info -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <h4>
                        <?php if (!empty($search) || !empty($category) || !empty($location)): ?>
                            Search Results
                        <?php else: ?>
                            All Events
                        <?php endif ?>
                        <span class="text-muted">(<?php echo count($events); ?> events found)</span>
                    </h4>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-secondary active" id="gridView">
                            <i class="fas fa-th"></i> Grid
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="listView">
                            <i class="fas fa-list"></i> List
                        </button>
                    </div>
                </div>
            </div>

            <!-- Events Container -->
            <div class="row events-container" id="eventsContainer">
                <?php if (!empty($events)): ?>
                    <?php foreach ($events as $index => $event): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="event-card animate-on-scroll"
                                 data-event-id="<?php echo $event->id; ?>"
                                 data-category="<?php echo $event->category; ?>"
                                 style="animation-delay: <?php echo $index * 0.1; ?>s;">

                                <div class="event-image-container position-relative">
                                    <img src="<?php echo $event->image_url ?: 'https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'; ?>"
                                         alt="<?php echo htmlspecialchars($event->title); ?>"
                                         class="event-image">

                                    <div class="event-badge">
                                        <?php echo htmlspecialchars($event->category); ?>
                                    </div>

                                    <?php if ($event->available_tickets < 10): ?>
                                        <div class="position-absolute top-0 start-0 m-3">
                                            <span class="badge bg-warning text-dark">
                                                <i class="fas fa-fire me-1"></i>Almost Sold Out
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="event-content">
                                    <h5 class="event-title"><?php echo htmlspecialchars($event->title); ?></h5>

                                    <p class="event-description text-muted mb-3">
                                        <?php echo htmlspecialchars(substr($event->description, 0, 100)) . '...'; ?>
                                    </p>

                                    <div class="event-meta mb-3">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-calendar-alt text-primary me-2"></i>
                                            <span><?php echo formatDate($event->event_date); ?></span>
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-clock text-primary me-2"></i>
                                            <span><?php echo formatTime($event->event_time); ?></span>
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                            <span><?php echo htmlspecialchars($event->venue . ', ' . $event->location); ?></span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-ticket-alt text-primary me-2"></i>
                                            <span class="ticket-count"><?php echo $event->available_tickets; ?></span>
                                            <span class="text-muted ms-1">tickets left</span>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="event-price">
                                            <?php echo formatCurrency($event->price); ?>
                                        </div>
                                        <div class="event-actions">
                                            <a href="details.php?id=<?php echo $event->id; ?>"
                                               class="btn btn-outline-primary btn-sm me-2">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button class="btn btn-primary-modern btn-sm add-to-cart-btn">
                                                <i class="fas fa-cart-plus me-1"></i>
                                                Add to Cart
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h4>No Events Found</h4>
                        <p class="text-muted">Try adjusting your search criteria or browse all events.</p>
                        <a href="./" class="btn btn-primary-modern">
                            <i class="fas fa-calendar-alt me-2"></i>
                            Browse All Events
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="row mt-5">
                    <div class="col-12">
                        <nav aria-label="Events pagination">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category); ?>&location=<?php echo urlencode($location); ?>">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category); ?>&location=<?php echo urlencode($location); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category); ?>&location=<?php echo urlencode($location); ?>">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <script>
        // View toggle functionality
        document.getElementById('gridView').addEventListener('click', function() {
            document.getElementById('eventsContainer').className = 'row events-container';
            this.classList.add('active');
            document.getElementById('listView').classList.remove('active');
        });

        document.getElementById('listView').addEventListener('click', function() {
            document.getElementById('eventsContainer').className = 'events-container';
            this.classList.add('active');
            document.getElementById('gridView').classList.remove('active');
        });
    </script>
</body>
</html>
