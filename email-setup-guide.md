# 📧 Email Setup Guide - Enable Direct Customer Messages

## 🎯 Overview
This guide will help you enable customers to send messages directly to your Gmail (<EMAIL>) from the contact form on your Event Booking System.

## ✅ Current Status
- ✅ **PHPMailer installed**: Email library ready
- ✅ **Contact form created**: Professional contact page with categories
- ✅ **Email templates**: HTML email templates designed
- ✅ **SMTP configuration**: Gmail SMTP settings configured
- ❌ **Gmail App Password**: **MISSING - Required to send emails**

## 🔧 Step-by-Step Setup

### Step 1: Enable 2-Factor Authentication on Gmail
1. Go to your **Google Account**: https://myaccount.google.com/
2. Click **Security** in the left sidebar
3. Under "Signing in to Google", click **2-Step Verification**
4. Follow the prompts to enable 2-factor authentication
5. **Note**: You must have 2FA enabled to create App Passwords

### Step 2: Generate Gmail App Password
1. After enabling 2FA, go back to **Security** settings
2. Under "Signing in to Google", click **2-Step Verification**
3. Scroll down and click **App passwords**
4. You may need to sign in again
5. Select **Mail** from the "Select app" dropdown
6. Select **Other (Custom name)** from the "Select device" dropdown
7. Enter: **Event Booking System**
8. Click **Generate**
9. **Copy the 16-character password** (it looks like: `abcd efgh ijkl mnop`)

### Step 3: Add App Password to Configuration
1. Open the file: `includes/config.php`
2. Find line 34: `define('SMTP_PASSWORD', '');`
3. Replace it with: `define('SMTP_PASSWORD', 'your-app-password-here');`
4. **Example**: `define('SMTP_PASSWORD', 'abcd efgh ijkl mnop');`
5. **Important**: Use the App Password, NOT your regular Gmail password

### Step 4: Test Email Functionality
1. Visit: `http://localhost:8000/test-email.php`
2. Click **Send Test Email**
3. Check your Gmail inbox for the test email
4. If successful, the contact form will work!

## 📝 Contact Form Features

### Customer Experience:
- **Professional contact form** at `/contact.php`
- **Multiple subject categories**:
  - General Inquiry
  - Booking Support
  - Event Information
  - Technical Issue
  - Refund Request
  - Partnership Opportunity
  - Other

### Message Categories:
- **Form validation**: Ensures all required fields are filled
- **Security**: CSRF protection against spam
- **Character limits**: Prevents abuse
- **Email formatting**: Professional HTML email templates

### What You'll Receive:
- **Sender information**: Name and email address
- **Subject categorization**: Easy to sort and prioritize
- **Message content**: Full customer message
- **Timestamp**: When the message was sent
- **Professional formatting**: Clean, readable email layout

## 🎨 Email Template Preview

When customers send messages, you'll receive emails like this:

```
Subject: Contact Form: Booking Support

From: John Doe (<EMAIL>)

📧 New Contact Message

From: John Doe
Email: <EMAIL>
Subject: Booking Support

Message:
Hello Fritz,

I'm interested in booking your event management services for a corporate event in Douala. Could you please provide more information about your packages and pricing?

Thank you!

Sent: January 27, 2025 at 3:45 PM
```

## 🚀 Quick Actions Available

### For Customers:
1. **Contact Form**: Fill out detailed form with categories
2. **Quick Email**: Pre-filled email templates
3. **WhatsApp**: Direct WhatsApp messaging
4. **Phone Call**: Click-to-call functionality
5. **Floating Contact Button**: Always accessible on every page

### For You:
- **Receive emails**: All messages <NAME_EMAIL>
- **Easy replies**: Reply directly from Gmail
- **Message tracking**: See when messages were sent
- **Professional appearance**: Builds customer trust

## 🔒 Security Features

- **CSRF Protection**: Prevents spam and automated submissions
- **Input Validation**: Sanitizes all user input
- **Rate Limiting**: Prevents abuse (can be added if needed)
- **Email Verification**: Validates email addresses
- **Character Limits**: Prevents overly long messages

## 📱 Mobile Optimization

- **Responsive design**: Works perfectly on mobile devices
- **Touch-friendly**: Large buttons and form fields
- **Fast loading**: Optimized for mobile networks
- **Native integration**: Phone and WhatsApp open native apps

## 🛠️ Troubleshooting

### If emails aren't sending:
1. **Check App Password**: Ensure it's correctly entered in config.php
2. **Verify 2FA**: Make sure 2-factor authentication is enabled
3. **Test connection**: Use the test email page
4. **Check Gmail**: Look in spam folder initially
5. **Server logs**: Check for error messages

### Common Issues:
- **"Could not authenticate"**: Wrong App Password
- **"Connection failed"**: Internet/firewall issues
- **"Emails in spam"**: Normal initially, improves over time
- **"Form not submitting"**: JavaScript or validation errors

## 📊 Analytics & Tracking

The system tracks:
- **Contact method usage**: Which methods customers prefer
- **Form submissions**: How many messages are sent
- **Popular subjects**: Most common inquiry types
- **Response patterns**: Peak contact times

## 🎯 Next Steps After Setup

1. **Test thoroughly**: Send test messages from different devices
2. **Monitor inbox**: Check for customer messages
3. **Set up filters**: Create Gmail filters for organization
4. **Response templates**: Create quick reply templates
5. **Auto-responder**: Consider setting up automatic acknowledgments

## 📞 Support

If you need help with the setup:
- **Email**: Use the contact form once it's working!
- **WhatsApp**: +237 651 408 682
- **Phone**: +237 651 408 682

## ✅ Checklist

- [ ] Enable 2-Factor Authentication on Gmail
- [ ] Generate Gmail App Password
- [ ] Add App Password to includes/config.php
- [ ] Test email functionality
- [ ] Send test message from contact form
- [ ] Verify email received in Gmail
- [ ] Set up Gmail filters (optional)
- [ ] Create response templates (optional)

Once completed, customers will be able to send messages directly to your Gmail from your website!
