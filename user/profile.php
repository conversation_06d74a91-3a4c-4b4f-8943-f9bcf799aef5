<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../auth/login.php');
}

$pageTitle = 'My Profile';
$user = $userManager->getUserById($_SESSION['user_id']);

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $updateData = [
        'first_name' => trim($_POST['first_name']),
        'last_name' => trim($_POST['last_name']),
        'phone' => trim($_POST['phone']),
        'address' => trim($_POST['address'])
    ];

    // Validate input
    $errors = [];
    if (empty($updateData['first_name'])) {
        $errors[] = 'First name is required';
    }
    if (empty($updateData['last_name'])) {
        $errors[] = 'Last name is required';
    }

    if (empty($errors)) {
        if ($userManager->updateProfile($_SESSION['user_id'], $updateData)) {
            setFlashMessage('success', 'Profile updated successfully!');
            // Update session data
            $_SESSION['first_name'] = $updateData['first_name'];
            $_SESSION['last_name'] = $updateData['last_name'];
            redirect('profile.php');
        } else {
            setFlashMessage('error', 'Failed to update profile. Please try again.');
        }
    } else {
        setFlashMessage('error', implode('<br>', $errors));
    }
}

$flashMessage = getFlashMessage();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">

    <style>
        .profile-hero {
            background: var(--hero-gradient);
            padding: 2rem 0;
            margin-top: 76px;
        }

        .profile-avatar {
            width: 120px;
            height: 120px;
            background: var(--secondary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            border: 4px solid white;
            box-shadow: 0 10px 30px rgba(64, 134, 129, 0.2);
        }

        .profile-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 60px rgba(64, 134, 129, 0.1);
            overflow: hidden;
            position: relative;
        }

        .profile-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .quick-action-card {
            background: var(--secondary-light);
            border: 1px solid rgba(64, 134, 129, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            height: 100%;
        }

        .quick-action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(64, 134, 129, 0.15);
            border-color: var(--primary-color);
        }

        .quick-action-icon {
            width: 60px;
            height: 60px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.5rem;
        }

        .form-section {
            background: var(--secondary-light);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(64, 134, 129, 0.1);
        }

        .section-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .profile-stats {
            background: var(--primary-gradient);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
        }

        .stat-item {
            margin-bottom: 1rem;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                EventHub
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../events/">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../events/search.php">Search</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="../booking/cart.php">
                            <i class="fas fa-shopping-cart cart-icon"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle" style="display: none;">0</span>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($user->first_name); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="dashboard.php">Dashboard</a></li>
                            <li><a class="dropdown-item active" href="profile.php">Profile</a></li>
                            <?php if (isAdmin()): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../admin/">Admin Panel</a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Profile Hero Section -->
    <section class="profile-hero">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-4 text-center">
                    <div class="profile-avatar">
                        <i class="fas fa-user fa-3x text-white"></i>
                    </div>
                </div>
                <div class="col-md-8 text-center text-md-start">
                    <h1 class="text-white fw-bold mb-2">
                        <?php echo htmlspecialchars($user->first_name . ' ' . $user->last_name); ?>
                    </h1>
                    <p class="text-white-50 mb-3">
                        <i class="fas fa-envelope me-2"></i><?php echo htmlspecialchars($user->email); ?>
                    </p>
                    <div class="d-flex justify-content-center justify-content-md-start gap-3">
                        <span class="badge bg-light text-dark px-3 py-2">
                            <i class="fas fa-crown me-1"></i><?php echo ucfirst($user->role); ?>
                        </span>
                        <span class="badge bg-light text-dark px-3 py-2">
                            <i class="fas fa-calendar me-1"></i>Member since <?php echo date('M Y', strtotime($user->created_at)); ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main style="background: var(--secondary-light); min-height: 100vh; padding: 2rem 0;">
        <div class="container">
            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <h3 class="section-title">
                        <i class="fas fa-bolt"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <h6 class="fw-bold mb-2">Dashboard</h6>
                        <p class="text-muted small mb-3">View your bookings and activity</p>
                        <a href="dashboard.php" class="btn btn-primary-modern btn-sm">Go to Dashboard</a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <h6 class="fw-bold mb-2">My Cart</h6>
                        <p class="text-muted small mb-3">Review items in your cart</p>
                        <a href="../booking/cart.php" class="btn btn-primary-modern btn-sm">View Cart</a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="fas fa-calendar"></i>
                        </div>
                        <h6 class="fw-bold mb-2">Browse Events</h6>
                        <p class="text-muted small mb-3">Discover amazing events</p>
                        <a href="../events/" class="btn btn-primary-modern btn-sm">Browse Events</a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h6 class="fw-bold mb-2">Search Events</h6>
                        <p class="text-muted small mb-3">Find specific events</p>
                        <a href="../events/search.php" class="btn btn-primary-modern btn-sm">Search Now</a>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Profile Stats -->
                <div class="col-lg-4 mb-4">
                    <div class="profile-stats">
                        <h5 class="fw-bold mb-4">
                            <i class="fas fa-chart-bar me-2"></i>
                            Your Stats
                        </h5>
                        <div class="stat-item">
                            <div class="stat-number">0</div>
                            <div class="stat-label">Total Bookings</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">0</div>
                            <div class="stat-label">Events Attended</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">0</div>
                            <div class="stat-label">Favorite Events</div>
                        </div>
                    </div>
                </div>

                <!-- Profile Form -->
                <div class="col-lg-8">
                    <?php if ($flashMessage): ?>
                        <div class="alert alert-<?php echo $flashMessage['type'] === 'error' ? 'danger' : $flashMessage['type']; ?> alert-dismissible fade show">
                            <?php echo $flashMessage['message']; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <div class="profile-card">
                        <div class="card-body p-0">
                            <div class="form-section">
                                <h4 class="section-title">
                                    <i class="fas fa-user-edit"></i>
                                    Personal Information
                                </h4>
                            <form method="POST" action="">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="first_name" class="form-label">First Name *</label>
                                        <input type="text" class="form-control form-control-modern" id="first_name"
                                               name="first_name" value="<?php echo htmlspecialchars($user->first_name); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="last_name" class="form-label">Last Name *</label>
                                        <input type="text" class="form-control form-control-modern" id="last_name"
                                               name="last_name" value="<?php echo htmlspecialchars($user->last_name); ?>" required>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control form-control-modern" id="email"
                                               value="<?php echo htmlspecialchars($user->email); ?>" readonly>
                                        <div class="form-text">Email cannot be changed. Contact support if needed.</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="username" class="form-label">Username</label>
                                        <input type="text" class="form-control form-control-modern" id="username"
                                               value="<?php echo htmlspecialchars($user->username); ?>" readonly>
                                        <div class="form-text">Username cannot be changed.</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control form-control-modern" id="phone"
                                               name="phone" value="<?php echo htmlspecialchars($user->phone); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="member_since" class="form-label">Member Since</label>
                                        <input type="text" class="form-control form-control-modern" id="member_since"
                                               value="<?php echo formatDate($user->created_at); ?>" readonly>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="address" class="form-label">Address</label>
                                    <textarea class="form-control form-control-modern" id="address" name="address"
                                              rows="3"><?php echo htmlspecialchars($user->address); ?></textarea>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="dashboard.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                                    </a>
                                    <button type="submit" class="btn btn-primary-modern">
                                        <i class="fas fa-save me-2"></i>Update Profile
                                    </button>
                                </div>
                            </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <!-- Load cart count -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadCartCount();
        });
    </script>
</body>
</html>
