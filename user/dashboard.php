<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Require login
requireLogin();

$pageTitle = 'Dashboard';
$userId = $_SESSION['user_id'];

// Get user's bookings
$userBookings = $bookingManager->getUserBookings($userId);

// Get user's cart count
$cartCount = $cartManager->getCartCount($userId);

// Get user info
$user = $userManager->getUserById($userId);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                ZARA-Events
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../events/">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">Dashboard</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="../booking/cart.php">
                            <i class="fas fa-shopping-cart cart-icon"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle" <?php echo $cartCount > 0 ? '' : 'style="display: none;"'; ?>>
                                <?php echo $cartCount; ?>
                            </span>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($user->first_name); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="dashboard.php">Dashboard</a></li>
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <?php if (isAdmin()): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../admin/">Admin Panel</a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header Section -->
    <section class="py-5 mt-5" style="background: var(--primary-gradient);">
        <div class="container">
            <div class="row">
                <div class="col-12 text-white">
                    <h1 class="display-5 fw-bold mb-3">
                        Welcome back, <?php echo htmlspecialchars($user->first_name); ?>!
                    </h1>
                    <p class="lead">Manage your bookings and discover new events</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Dashboard Content -->
    <section class="py-5">
        <div class="container">
            <!-- Stats Cards -->
            <div class="row mb-5">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card-modern text-center p-4 h-100">
                        <div class="mb-3">
                            <i class="fas fa-ticket-alt fa-3x" style="background: var(--primary-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                        </div>
                        <h4 class="fw-bold"><?php echo count($userBookings); ?></h4>
                        <p class="text-muted mb-0">Total Bookings</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card-modern text-center p-4 h-100">
                        <div class="mb-3">
                            <i class="fas fa-shopping-cart fa-3x" style="background: var(--success-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                        </div>
                        <h4 class="fw-bold"><?php echo $cartCount; ?></h4>
                        <p class="text-muted mb-0">Items in Cart</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card-modern text-center p-4 h-100">
                        <div class="mb-3">
                            <i class="fas fa-calendar-check fa-3x" style="background: var(--warning-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                        </div>
                        <h4 class="fw-bold">
                            <?php
                            $upcomingCount = 0;
                            foreach ($userBookings as $booking) {
                                if (strtotime($booking->event_date) > time()) {
                                    $upcomingCount++;
                                }
                            }
                            echo $upcomingCount;
                            ?>
                        </h4>
                        <p class="text-muted mb-0">Upcoming Events</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card-modern text-center p-4 h-100">
                        <div class="mb-3">
                            <i class="fas fa-star fa-3x" style="background: var(--danger-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                        </div>
                        <h4 class="fw-bold">
                            <?php echo $user->role === 'admin' ? 'Admin' : 'Member'; ?>
                        </h4>
                        <p class="text-muted mb-0">Account Status</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-5">
                <div class="col-12">
                    <div class="card-modern p-4">
                        <h5 class="fw-bold mb-3">Quick Actions</h5>
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="../events/" class="btn btn-primary-modern w-100">
                                    <i class="fas fa-search me-2"></i>
                                    Browse Events
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="../booking/cart.php" class="btn btn-secondary-modern w-100">
                                    <i class="fas fa-shopping-cart me-2"></i>
                                    View Cart
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="profile.php" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-user-edit me-2"></i>
                                    Edit Profile
                                </a>
                            </div>
                            <?php if (isAdmin()): ?>
                                <div class="col-md-3 mb-3">
                                    <a href="../admin/" class="btn btn-outline-danger w-100">
                                        <i class="fas fa-cog me-2"></i>
                                        Admin Panel
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Bookings -->
            <div class="row">
                <div class="col-12">
                    <div class="card-modern p-4">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="fw-bold mb-0">Recent Bookings</h5>
                            <a href="bookings.php" class="btn btn-outline-primary btn-sm">View All</a>
                        </div>

                        <?php if (!empty($userBookings)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Event</th>
                                            <th>Date</th>
                                            <th>Tickets</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach (array_slice($userBookings, 0, 5) as $booking): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="<?php echo $booking->image_url ?: 'https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80'; ?>"
                                                             alt="<?php echo htmlspecialchars($booking->title); ?>"
                                                             class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                                        <div>
                                                            <h6 class="mb-0"><?php echo htmlspecialchars($booking->title); ?></h6>
                                                            <small class="text-muted"><?php echo htmlspecialchars($booking->venue); ?></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <div><?php echo formatDate($booking->event_date); ?></div>
                                                        <small class="text-muted"><?php echo formatTime($booking->event_time); ?></small>
                                                    </div>
                                                </td>
                                                <td><?php echo $booking->quantity; ?></td>
                                                <td><?php echo formatCurrency($booking->total_amount); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $booking->booking_status === 'confirmed' ? 'success' : ($booking->booking_status === 'pending' ? 'warning' : 'danger'); ?>">
                                                        <?php echo ucfirst($booking->booking_status); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="../booking/confirmation.php?references=<?php echo $booking->booking_reference; ?>"
                                                       class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                <h5>No Bookings Yet</h5>
                                <p class="text-muted">Start exploring events and make your first booking!</p>
                                <a href="../events/" class="btn btn-primary-modern">
                                    <i class="fas fa-search me-2"></i>
                                    Browse Events
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>
</body>
</html>
