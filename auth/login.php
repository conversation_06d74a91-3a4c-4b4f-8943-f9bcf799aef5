<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

$pageTitle = 'Login';
$message = '';
$messageType = '';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: ../user/dashboard.php');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'])) {
        $message = 'Invalid security token. Please try again.';
        $messageType = 'danger';
    } else {
        $username = sanitizeInput($_POST['username']);
        $password = $_POST['password'];

        if ($userManager->login($username, $password)) {
            // Redirect based on user role
            if (isAdmin()) {
                header('Location: ../admin/');
            } else {
                header('Location: ../user/dashboard.php');
            }
            exit;
        } else {
            $message = 'Invalid username or password.';
            $messageType = 'danger';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                ZARA-Events
            </a>

            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../">Home</a>
                <a class="nav-link" href="register.php">Sign Up</a>
            </div>
        </div>
    </nav>

    <!-- Login Section -->
    <section class="hero-modern d-flex align-items-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-5 col-md-7">
                    <div class="form-modern animate-on-scroll">
                        <div class="text-center mb-4">
                            <h2 class="fw-bold mb-2">Welcome Back!</h2>
                            <p class="text-muted">Sign in to your account to continue</p>
                        </div>

                        <?php if (!empty($message)): ?>
                            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                                <?php echo $message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <form method="POST" id="loginForm">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                            <div class="mb-3">
                                <label for="username" class="form-label">Username or Email</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-user"></i>
                                    </span>
                                    <input type="text" class="form-control form-control-modern"
                                           id="username" name="username" required
                                           placeholder="Enter your username or email">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control form-control-modern"
                                           id="password" name="password" required
                                           placeholder="Enter your password">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="rememberMe">
                                <label class="form-check-label" for="rememberMe">
                                    Remember me
                                </label>
                            </div>

                            <button type="submit" class="btn btn-primary-modern w-100 mb-3">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Sign In
                            </button>

                            <div class="text-center">
                                <p class="mb-2">
                                    <a href="forgot-password.php" class="text-decoration-none">Forgot your password?</a>
                                </p>
                                <p class="text-muted">
                                    Don't have an account?
                                    <a href="register.php" class="text-decoration-none fw-bold">Sign up here</a>
                                </p>
                            </div>
                        </form>

                        <!-- Demo Credentials -->
                        <div class="mt-4 p-3" style="background: rgba(0,0,0,0.05); border-radius: 12px;">
                            <h6 class="fw-bold mb-2">Demo Credentials:</h6>
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted d-block">Admin Access:</small>
                                    <small><strong>admin</strong> / <strong>admin123</strong></small>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted d-block">User Access:</small>
                                    <small><strong>user</strong> / <strong>user123</strong></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');

            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // Form validation
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;

            if (!username || !password) {
                e.preventDefault();
                alert('Please fill in all fields.');
                return false;
            }
        });

        // Auto-fill demo credentials
        document.addEventListener('click', function(e) {
            if (e.target.closest('.demo-credentials')) {
                const isAdmin = e.target.textContent.includes('admin');
                document.getElementById('username').value = isAdmin ? 'admin' : 'user';
                document.getElementById('password').value = isAdmin ? 'admin123' : 'user123';
            }
        });
    </script>
</body>
</html>
