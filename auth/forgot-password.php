<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

$pageTitle = 'Forgot Password';
$message = '';
$messageType = '';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: ../user/dashboard.php');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'])) {
        $message = 'Invalid security token. Please try again.';
        $messageType = 'danger';
    } else {
        $email = sanitizeInput($_POST['email']);
        
        if (empty($email)) {
            $message = 'Please enter your email address.';
            $messageType = 'danger';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $message = 'Please enter a valid email address.';
            $messageType = 'danger';
        } else {
            // Always show success message for security (don't reveal if email exists)
            if ($userManager->requestPasswordReset($email)) {
                $message = 'If an account with that email exists, we\'ve sent you a password reset link.';
                $messageType = 'success';
            } else {
                $message = 'If an account with that email exists, we\'ve sent you a password reset link.';
                $messageType = 'success';
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                ZARA-Events
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../">Home</a>
                <a class="nav-link" href="login.php">Sign In</a>
                <a class="nav-link" href="register.php">Sign Up</a>
            </div>
        </div>
    </nav>

    <!-- Forgot Password Section -->
    <section class="hero-modern d-flex align-items-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-5 col-md-7">
                    <div class="form-modern animate-on-scroll">
                        <div class="text-center mb-4">
                            <div class="mb-3">
                                <i class="fas fa-key fa-3x" style="color: var(--primary-color);"></i>
                            </div>
                            <h2 class="fw-bold mb-2">Forgot Password?</h2>
                            <p class="text-muted">No worries! Enter your email and we'll send you a reset link.</p>
                        </div>

                        <?php if (!empty($message)): ?>
                            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                                <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                                <?php echo $message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <form method="POST" id="forgotPasswordForm">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            
                            <div class="form-floating-modern">
                                <input type="email" class="form-control-modern" 
                                       id="email" name="email" required 
                                       placeholder=" " value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                                <label for="email">Email Address</label>
                            </div>

                            <button type="submit" class="btn btn-primary-enhanced w-100 mb-3">
                                <i class="fas fa-paper-plane me-2"></i>
                                Send Reset Link
                            </button>

                            <div class="text-center">
                                <p class="mb-2">
                                    <a href="login.php" class="text-decoration-none">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        Back to Sign In
                                    </a>
                                </p>
                                <p class="text-muted">
                                    Don't have an account? 
                                    <a href="register.php" class="text-decoration-none fw-bold">Sign up here</a>
                                </p>
                            </div>
                        </form>

                        <!-- Security Notice -->
                        <div class="mt-4 p-3" style="background: rgba(16, 185, 129, 0.1); border-radius: 12px; border-left: 4px solid #10b981;">
                            <h6 class="fw-bold mb-2" style="color: #065f46;">
                                <i class="fas fa-shield-alt me-2"></i>
                                Security Notice
                            </h6>
                            <small class="text-muted">
                                For your security, password reset links expire after 1 hour. 
                                If you don't receive an email, check your spam folder or try again.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Toast Container -->
    <div class="toast-container"></div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner-modern"></div>
            <div class="loading-text">Sending reset link...</div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>
    
    <script>
        // Form validation and submission
        document.getElementById('forgotPasswordForm').addEventListener('submit', function(e) {
            const email = document.getElementById('email').value.trim();
            
            if (!email) {
                e.preventDefault();
                showToast('Please enter your email address.', 'error');
                return false;
            }
            
            if (!isValidEmail(email)) {
                e.preventDefault();
                showToast('Please enter a valid email address.', 'error');
                return false;
            }
            
            // Show loading overlay
            document.getElementById('loadingOverlay').classList.add('show');
        });

        // Email validation function
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // Enhanced floating label behavior
        document.querySelectorAll('.form-control-modern').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });
            
            input.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });
            
            // Check if field has value on page load
            if (input.value) {
                input.parentElement.classList.add('focused');
            }
        });
    </script>
</body>
</html>
